#!/bin/bash
# Script to compile PhD thesis with texlive 2013

# Set paths
TEXLIVE_BIN="/prog/texlive/2013/bin/x86_64-linux"
#PDFLATEX="$TEXLIVE_BIN/pdflatex"
#BIBTEX="$TEXLIVE_BIN/bibtex"
PDFLATEX="/usr/bin/pdflatex"

BIBTEX="/usr/bin/bibtex"


# Check if pdflatex exists
if [ ! -f "$PDFLATEX" ]; then
    echo "Error: pdflatex not found at $PDFLATEX"
    exit 1
fi

echo "Using pdflatex from: $PDFLATEX"
echo "Starting thesis compilation..."

# First pass
echo "First pass..."
$PDFLATEX -interaction=nonstopmode thesis.tex

# Second pass for cross-references
echo "Second pass..."
$PDFLATEX -interaction=nonstopmode thesis.tex

# Check if PDF was created
if [ -f "thesis.pdf" ]; then
    echo "Success! Thesis compiled to thesis.pdf"
    ls -lh thesis.pdf
else
    echo "Error: thesis.pdf was not created"
    # Show last few lines of log for debugging
    if [ -f "thesis.log" ]; then
        echo "Last 20 lines of thesis.log:"
        tail -20 thesis.log
    fi
fi

# Clean up auxiliary files (optional)
# rm -f *.aux *.log *.out *.toc *.lof *.lot chapters/*.aux appendices/*.aux
