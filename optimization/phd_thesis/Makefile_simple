# Simple Makefile for PhD Thesis Compilation
# Compiles directly in main directory to handle chapter includes

LATEX = pdflatex
MAIN = thesis

# Default target
all: compile

# Simple compilation without output directory
compile:
	@echo "Compiling thesis..."
	$(LATEX) $(MAIN).tex
	@echo "First pass complete"
	
	# Run again for references
	$(LATEX) $(MAIN).tex
	@echo "Second pass complete"
	
	@echo "Compilation finished! Output: $(MAIN).pdf"

# Clean auxiliary files
clean:
	rm -f *.aux *.log *.out *.toc *.lof *.lot *.bbl *.blg *.loa
	rm -f chapters/*.aux
	rm -f appendices/*.aux

# Clean everything
cleanall: clean
	rm -f $(MAIN).pdf

.PHONY: all compile clean cleanall