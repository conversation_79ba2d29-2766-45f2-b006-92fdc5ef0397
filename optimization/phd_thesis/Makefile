# Makefile for PhD Thesis Compilation
# Wind Farm Layout Optimization with Differentiable Flow Models

# LaTeX compiler
LATEX = pdflatex
BIBTEX = bibtex
MAKEINDEX = makeindex

# Main document
MAIN = thesis
CHAPTERS = chapters/*.tex
APPENDICES = appendices/*.tex
BIBFILE = references.bib

# Output directory
OUTDIR = build

# Figures directory
FIGDIR = figures

# All source files
SOURCES = $(MAIN).tex $(CHAPTERS) $(APPENDICES) $(BIBFILE)

# Default target
all: $(MAIN).pdf

# Main compilation rule
$(MAIN).pdf: $(SOURCES)
	@echo "===================="
	@echo "Building PhD Thesis"
	@echo "===================="
	@mkdir -p $(OUTDIR)
	
	# First pass
	$(LATEX) -output-directory=$(OUTDIR) $(MAIN)
	
	# Bibliography
	cp $(BIBFILE) $(OUTDIR)/
	cd $(OUTDIR) && $(BIBTEX) $(MAIN)
	
	# Second pass (citations)
	$(LATEX) -output-directory=$(OUTDIR) $(MAIN)
	
	# Third pass (references)
	$(LATEX) -output-directory=$(OUTDIR) $(MAIN)
	
	# Move final PDF to main directory
	mv $(OUTDIR)/$(MAIN).pdf ./
	
	@echo "===================="
	@echo "Build complete!"
	@echo "Output: $(MAIN).pdf"
	@echo "===================="

# Quick build (single pass)
quick:
	@echo "Quick build (single pass)..."
	@mkdir -p $(OUTDIR)
	$(LATEX) -output-directory=$(OUTDIR) $(MAIN)
	mv $(OUTDIR)/$(MAIN).pdf ./

# Clean intermediate files
clean:
	@echo "Cleaning intermediate files..."
	rm -rf $(OUTDIR)
	rm -f *.aux *.log *.out *.toc *.lof *.lot *.bbl *.blg
	rm -f chapters/*.aux
	rm -f appendices/*.aux

# Clean everything including PDF
cleanall: clean
	@echo "Removing PDF output..."
	rm -f $(MAIN).pdf

# Create figure directories
figures:
	@echo "Creating figure directories..."
	mkdir -p $(FIGDIR)
	mkdir -p $(FIGDIR)/ch4_differentiable_floris
	mkdir -p $(FIGDIR)/ch5_multi_framework
	mkdir -p $(FIGDIR)/ch6_hybrid_optimization
	mkdir -p $(FIGDIR)/ch7_case_studies

# Generate sample figures (requires Python environment)
generate-figures:
	@echo "Generating thesis figures..."
	cd ../ && python3 generate_thesis_figures.py

# View PDF
view: $(MAIN).pdf
	@echo "Opening PDF viewer..."
	@if command -v open >/dev/null 2>&1; then \
		open $(MAIN).pdf; \
	elif command -v xdg-open >/dev/null 2>&1; then \
		xdg-open $(MAIN).pdf; \
	elif command -v evince >/dev/null 2>&1; then \
		evince $(MAIN).pdf; \
	else \
		echo "No PDF viewer found. Please open $(MAIN).pdf manually."; \
	fi

# Check LaTeX installation
check:
	@echo "Checking LaTeX installation..."
	@which $(LATEX) || (echo "ERROR: pdflatex not found" && exit 1)
	@which $(BIBTEX) || (echo "ERROR: bibtex not found" && exit 1)
	@echo "✓ LaTeX tools found"
	@echo ""
	@echo "Checking required packages..."
	@kpsewhich amsmath.sty >/dev/null || echo "WARNING: amsmath package not found"
	@kpsewhich graphicx.sty >/dev/null || echo "WARNING: graphicx package not found"
	@kpsewhich hyperref.sty >/dev/null || echo "WARNING: hyperref package not found"
	@echo "✓ Basic packages checked"

# Word count
wordcount:
	@echo "Counting words in thesis..."
	@texcount -inc $(MAIN).tex | grep "Words in text"

# Help
help:
	@echo "PhD Thesis Makefile Commands:"
	@echo "  make          - Full build with bibliography"
	@echo "  make quick    - Quick build (single pass)"
	@echo "  make clean    - Remove intermediate files"
	@echo "  make cleanall - Remove all generated files"
	@echo "  make figures  - Create figure directories"
	@echo "  make view     - Open PDF in viewer"
	@echo "  make check    - Check LaTeX installation"
	@echo "  make wordcount- Count words in thesis"
	@echo "  make help     - Show this help message"

# Phony targets
.PHONY: all quick clean cleanall figures generate-figures view check wordcount help
