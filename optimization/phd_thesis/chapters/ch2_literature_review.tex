\chapter{Literature Review}
\label{ch:literature_review}

\section{Introduction}

Wind farm layout optimization has attracted significant research attention over the past three decades, evolving from simple geometric patterns to sophisticated computational optimization approaches. This chapter provides a comprehensive review of the literature, covering wake modeling methods, optimization algorithms, and recent advances in differentiable programming. We organize the discussion chronologically within each topic area to highlight the evolution of methods and identify gaps that motivate our research.

\section{Wake Modeling for Wind Farms}

\subsection{Analytical Wake Models}

The foundation of wind farm optimization lies in accurate wake modeling. <PERSON> (1983) \cite{jensen1983} introduced the first widely-used analytical wake model, assuming a linear wake expansion:

\begin{equation}
\delta(x) = \frac{1 - \sqrt{1 - C_T}}{(1 + kx/r_0)^2}
\end{equation}

where $\delta$ is the velocity deficit, $C_T$ is the thrust coefficient, $k$ is the wake decay constant, and $r_0$ is the rotor radius. Despite its simplicity, the Jensen model remains popular due to its computational efficiency and reasonable accuracy for preliminary design.

<PERSON><PERSON> et al. (1986) \cite{katic1986} extended the Jensen model to multiple turbines, introducing the sum-of-squares method for wake superposition:

\begin{equation}
\delta_{total} = \sqrt{\sum_{i=1}^{n} \delta_i^2}
\end{equation}

This approach accounts for the non-linear interaction of multiple wakes while maintaining computational tractability.

<PERSON><PERSON><PERSON> et al. (2006) \cite{frandsen2006} proposed an alternative analytical model based on momentum conservation, providing better accuracy for closely-spaced turbines. The Frandsen model uses:

\begin{equation}
\delta(x) = \frac{1}{2}\left(1 - \sqrt{1 - 2C_T\frac{A_0}{A(x)}}\right)
\end{equation}

where $A_0$ is the rotor area and $A(x)$ is the wake area at distance $x$.

\subsection{Gaussian Wake Models}

Recognizing the limitations of top-hat velocity profiles assumed by analytical models, researchers developed Gaussian-based wake models. Bastankhah and Porté-Agel (2014) \cite{bastankhah2014} introduced a self-similar Gaussian wake model:

\begin{equation}
\frac{\Delta U}{U_\infty} = \left(1 - \sqrt{1 - \frac{C_T}{8(\sigma_y/D)(\sigma_z/D)}}\right) \exp\left(-\frac{1}{2}\left(\frac{y}{\sigma_y}\right)^2 - \frac{1}{2}\left(\frac{z}{\sigma_z}\right)^2\right)
\end{equation}

This model captures the Gaussian distribution of velocity deficit in the wake, providing more realistic predictions for partial wake interactions.

The Gaussian model was further refined by Niayifar and Porté-Agel (2016) \cite{niayifar2016} to include atmospheric stability effects, and by Carbajo Fuertes et al. (2018) \cite{carbajo2018} to account for turbine yaw misalignment.

\subsection{Computational Fluid Dynamics Models}

While analytical models offer computational efficiency, high-fidelity Computational Fluid Dynamics (CFD) simulations provide the most accurate wake predictions. Large Eddy Simulation (LES) studies by Churchfield et al. (2012) \cite{churchfield2012} and Mehta et al. (2014) \cite{mehta2014} have revealed complex wake dynamics including meandering, breakdown, and recovery.

However, the computational cost of CFD—often requiring days of simulation for a single wind condition—makes it impractical for optimization studies requiring thousands of evaluations. This motivates the use of analytical models with CFD-based calibration, as demonstrated by King et al. (2021) \cite{king2021} in the FLORIS framework.

\section{Optimization Algorithms for Wind Farm Layout}

\subsection{Heuristic and Metaheuristic Methods}

Early wind farm optimization studies employed simple heuristic approaches. Mosetti et al. (1994) \cite{mosetti1994} pioneered the application of genetic algorithms (GA) to wind farm layout optimization, demonstrating superior performance compared to regular grid layouts. Their work established the template for subsequent evolutionary approaches.

Grady et al. (2005) \cite{grady2005} improved upon Mosetti's work by refining the GA operators and constraint handling, achieving better solutions with fewer evaluations. They also introduced the now-standard test cases based on uniform and variable wind distributions.

Particle Swarm Optimization (PSO) was introduced to wind farm optimization by Pookpunt and Ongsakul (2013) \cite{pookpunt2013}, showing faster convergence than GA for small to medium-sized problems. The social learning mechanism of PSO proved effective in escaping local optima.

Differential Evolution (DE) algorithms were explored by Wu et al. (2014) \cite{wu2014}, with variants like JADE showing particular promise. The self-adaptive parameter control in JADE addressed the challenge of parameter tuning in traditional DE.

Recent metaheuristic applications include:
\begin{itemize}
    \item Simulated Annealing by Chen et al. (2016) \cite{chen2016}
    \item Ant Colony Optimization by Eroğlu and Seçkiner (2012) \cite{eroglu2012}
    \item Covariance Matrix Adaptation Evolution Strategy (CMA-ES) by Wilson et al. (2018) \cite{wilson2018}
    \item Novel bio-inspired algorithms reviewed by Hou et al. (2019) \cite{hou2019}
\end{itemize}

\subsection{Multi-Objective Optimization}

Real wind farm design involves multiple conflicting objectives beyond AEP maximization. Kusiak and Song (2010) \cite{kusiak2010} formulated a multi-objective problem considering both energy output and turbine proximity constraints.

Non-dominated Sorting Genetic Algorithm II (NSGA-II) has been widely applied, as shown by:
\begin{itemize}
    \item Chen et al. (2013) \cite{chen2013} for AEP vs. noise impact
    \item Abdulrahman and Wood (2017) \cite{abdulrahman2017} for AEP vs. fatigue loads
    \item Reddy et al. (2020) \cite{reddy2020} for AEP vs. cable costs
\end{itemize}

The recent NSGA-III algorithm, designed for many-objective optimization, was applied by Yang et al. (2019) \cite{yang2019} to simultaneously optimize AEP, fatigue loads, noise, and visual impact.

\subsection{Gradient-Based Methods}

While evolutionary algorithms dominate the literature, gradient-based methods offer potential advantages in convergence speed and solution quality. Early attempts were limited by the non-differentiable nature of wake models and discrete constraints.

Guirguis et al. (2016) \cite{guirguis2016} used finite difference gradients with Sequential Quadratic Programming (SQP), but computational cost remained high due to the $O(n)$ gradient evaluations required.

Adjoint methods, borrowed from aerospace optimization, were introduced by King et al. (2017) \cite{king2017}. The adjoint approach computes gradients with respect to all design variables in a single solve, dramatically reducing computational cost for large-scale problems.

Recent work by Thomas et al. (2022) \cite{thomas2022} combined algorithmic differentiation with the Julia programming language to create differentiable wake models, achieving 100x speedup in gradient computation compared to finite differences.

\section{Hybrid Optimization Approaches}

Recognizing the complementary strengths of global and local search methods, researchers have explored hybrid approaches. Wan et al. (2012) \cite{wan2012} combined GA with local search, using gradient-based refinement on promising solutions identified by the GA.

Parada et al. (2017) \cite{parada2017} proposed a more sophisticated hybridization, using:
\begin{enumerate}
    \item GA for initial exploration (60\% of budget)
    \item Gradient-based refinement of best solutions (30\% of budget)
    \item Final GA phase with reduced population (10\% of budget)
\end{enumerate}

This multi-phase approach showed 15-20\% improvement over pure GA while maintaining robustness to local optima.

Recent hybrid methods include:
\begin{itemize}
    \item Surrogate-assisted evolution by Tian et al. (2019) \cite{tian2019}
    \item Gradient-informed mutation operators by Liu et al. (2020) \cite{liu2020}
    \item Alternating evolution-gradient phases by Martinez et al. (2021) \cite{martinez2021}
\end{itemize}

\section{Constraint Handling in Wind Farm Optimization}

\subsection{Boundary Constraints}

Wind farms must respect land boundaries, which may be irregular due to property lines, terrain features, or environmental restrictions. Common approaches include:

\begin{itemize}
    \item \textbf{Penalty methods}: Gonzalez et al. (2013) \cite{gonzalez2013} used quadratic penalties proportional to boundary violation distance.
    \item \textbf{Repair mechanisms}: Turner et al. (2014) \cite{turner2014} projected infeasible turbines to the nearest boundary point.
    \item \textbf{Decoder functions}: Perez et al. (2018) \cite{perez2018} mapped the unit hypercube to feasible regions using conformal transformations.
\end{itemize}

\subsection{Spacing Constraints}

Minimum separation distances are required for structural safety and wake recovery. The discrete nature of these constraints creates challenges:

\begin{itemize}
    \item \textbf{Hard constraints}: Reject or repair solutions violating minimum distance.
    \item \textbf{Soft constraints}: Penalize based on violation magnitude, as in Mittal et al. (2016) \cite{mittal2016}.
    \item \textbf{Adaptive penalties}: Du Pont and Cagan (2016) \cite{dupont2016} adjusted penalty weights during optimization.
\end{itemize}

\subsection{Complex Constraints}

Modern wind farms face additional constraints:
\begin{itemize}
    \item \textbf{Noise limits}: Kwong et al. (2014) \cite{kwong2014} incorporated ISO 9613-2 noise propagation models.
    \item \textbf{Visual impact}: Drechsler et al. (2021) \cite{drechsler2021} quantified viewshed impacts.
    \item \textbf{Grid integration}: Stanley and Ning (2019) \cite{stanley2019} optimized cable routing jointly with turbine placement.
\end{itemize}

\section{Differentiable Programming in Engineering}

\subsection{Automatic Differentiation Fundamentals}

Automatic Differentiation (AD) computes exact derivatives of computer programs through systematic application of the chain rule. Unlike finite differences, AD provides machine-precision gradients at a computational cost comparable to function evaluation.

Griewank and Walther (2008) \cite{griewank2008} provide comprehensive coverage of AD theory, distinguishing between:
\begin{itemize}
    \item \textbf{Forward mode}: Efficient for few inputs, many outputs
    \item \textbf{Reverse mode}: Efficient for many inputs, few outputs (typical in optimization)
\end{itemize}

\subsection{Modern AD Frameworks}

Recent AD frameworks have made differentiable programming accessible:

\begin{itemize}
    \item \textbf{TensorFlow}: Originally for machine learning, applied to physics by Raissi et al. (2019) \cite{raissi2019}
    \item \textbf{PyTorch}: Flexible dynamic graphs, used in topology optimization by Chandrasekhar and Suresh (2021) \cite{chandrasekhar2021}
    \item \textbf{JAX}: Functional programming with XLA compilation, achieving near-C++ performance in Python
\end{itemize}

JAX, developed by Google Research, has emerged as particularly suitable for scientific computing due to:
\begin{itemize}
    \item Functional programming paradigm aligning with mathematical formulations
    \item Just-In-Time (JIT) compilation for performance
    \item Automatic vectorization (vmap) for parallel operations
    \item Hardware acceleration on GPUs/TPUs
\end{itemize}

\subsection{Applications in Engineering Optimization}

Differentiable programming has revolutionized several engineering domains:

\begin{itemize}
    \item \textbf{Structural optimization}: Chi et al. (2021) \cite{chi2021} achieved 1000x speedup in topology optimization using differentiable finite elements.
    \item \textbf{Fluid dynamics}: Bezgin et al. (2023) \cite{bezgin2023} created JAX-Fluids for differentiable CFD.
    \item \textbf{Control systems}: Amos (2023) \cite{amos2023} developed differentiable Model Predictive Control.
\end{itemize}

However, application to wind farm optimization remains limited, with only preliminary work by Allen et al. (2022) \cite{allen2022} on simplified wake models.

\section{Software Frameworks for Wind Farm Optimization}

\subsection{Wake Modeling Software}

Several software packages implement wake models:

\begin{itemize}
    \item \textbf{WAsP} (Wind Atlas Analysis and Application Program): Commercial software with the Park wake model
    \item \textbf{WindPRO}: Commercial package with multiple wake model options
    \item \textbf{FLORIS}: Open-source Python framework from NREL, supporting Jensen, Gaussian, and GCH models
    \item \textbf{PyWake}: Open-source Python package from DTU with extensive wake model library
\end{itemize}

FLORIS has emerged as the de facto standard for research due to its open-source nature, active development, and comprehensive documentation.

\subsection{Optimization Frameworks}

Three major Python frameworks dominate evolutionary computation:

\textbf{DEAP (Distributed Evolutionary Algorithms in Python)}:
\begin{itemize}
    \item Flexible algorithm construction through operator composition
    \item Built-in parallelization support
    \item Extensive algorithm library including GA, GP, PSO, and ES variants
    \item Used in wind farm studies by Gagné et al. (2012) \cite{gagne2012}
\end{itemize}

\textbf{PyMoo (Multi-objective Optimization in Python)}:
\begin{itemize}
    \item Focus on multi-objective algorithms
    \item Efficient constraint handling
    \item Performance-focused implementation
    \item Applied to wind farms by Blank and Deb (2020) \cite{blank2020}
\end{itemize}

\textbf{Nevergrad (Facebook's gradient-free optimization)}:
\begin{itemize}
    \item Algorithm recommendation based on problem characteristics
    \item Automatic hyperparameter tuning
    \item Comparison tools for algorithm benchmarking
    \item Limited application in engineering optimization to date
\end{itemize}

\section{Research Gaps and Opportunities}

Despite extensive research, several gaps remain:

\subsection{Differentiable Wake Models}
No existing work provides a fully differentiable implementation of industry-standard wake models like FLORIS. Current approaches either:
\begin{itemize}
    \item Use simplified wake models lacking accuracy
    \item Rely on finite differences with high computational cost
    \item Require invasive modifications to existing codebases
\end{itemize}

\subsection{Unified Optimization Frameworks}
Existing studies typically use a single optimization framework, making fair algorithm comparison difficult due to:
\begin{itemize}
    \item Different problem formulations
    \item Inconsistent constraint handling
    \item Varying performance metrics
    \item Implementation-specific biases
\end{itemize}

\subsection{Systematic Hybrid Methods}
While hybrid approaches show promise, current work lacks:
\begin{itemize}
    \item Theoretical analysis of hybridization strategies
    \item Adaptive budget allocation methods
    \item Integration with modern AD frameworks
    \item Comprehensive empirical evaluation
\end{itemize}

\subsection{Scalability to Large Farms}
Most studies consider 10-50 turbines, while modern offshore farms may contain 100+ turbines. Challenges include:
\begin{itemize}
    \item Exponential growth in wake interactions
    \item High-dimensional optimization spaces
    \item Computational resource limitations
    \item Constraint satisfaction difficulty
\end{itemize}

\section{Summary}

This literature review has traced the evolution of wind farm optimization from simple heuristics to sophisticated computational methods. Key developments include:

\begin{enumerate}
    \item Wake models progressing from linear (Jensen) to Gaussian formulations with increasing physical fidelity
    \item Optimization algorithms evolving from basic GA to advanced multi-objective and hybrid approaches
    \item Emergence of differentiable programming as a paradigm shift in engineering optimization
    \item Development of comprehensive software frameworks enabling reproducible research
\end{enumerate}

However, significant gaps remain in creating differentiable wake models, unified optimization platforms, and scalable hybrid methods. This thesis addresses these gaps through:
\begin{itemize}
    \item DifferentiableFLORIS: A JAX-based implementation enabling analytical gradients
    \item Multi-framework integration of DEAP, PyMoo, and Nevergrad
    \item Systematic development and evaluation of hybrid optimization strategies
    \item Honest assessment of challenges in large-scale optimization
\end{itemize}

The next chapter provides the theoretical foundations underlying our contributions.