\chapter{Theoretical Background}
\label{ch:theoretical_background}

\section{Introduction}

This chapter provides the theoretical foundations for wind farm layout optimization, covering wake modeling physics, optimization theory, automatic differentiation, and constraint handling methods. We establish the mathematical framework that underlies the computational methods developed in subsequent chapters.

\section{Wind Turbine Aerodynamics}

\subsection{Actuator Disk Theory}

Wind turbines extract kinetic energy from moving air masses. The fundamental theory describing this process is the actuator disk model, which represents the turbine rotor as a permeable disk that extracts momentum from the flow.

The one-dimensional momentum theory yields the axial induction factor $a$:

\begin{equation}
a = \frac{u_\infty - u_d}{u_\infty}
\end{equation}

where $u_\infty$ is the free-stream velocity and $u_d$ is the velocity at the disk. The thrust force on the disk is:

\begin{equation}
T = \frac{1}{2}\rho A u_\infty^2 C_T
\end{equation}

where $\rho$ is air density, $A$ is the rotor swept area, and $C_T$ is the thrust coefficient. The relationship between thrust coefficient and axial induction factor is:

\begin{equation}
C_T = 4a(1-a)
\end{equation}

This relationship holds for $a < 0.5$. For higher induction factors, empirical corrections such as <PERSON><PERSON><PERSON>'s correction must be applied to account for turbulent wake states.

\subsection{Power Extraction}

The power extracted by a wind turbine is given by:

\begin{equation}
P = \frac{1}{2}\rho A u^3 C_P
\end{equation}

where $C_P$ is the power coefficient. The theoretical maximum power coefficient, known as the Betz limit, is:

\begin{equation}
C_{P,\max} = \frac{16}{27} \approx 0.593
\end{equation}

In practice, modern wind turbines achieve $C_P \approx 0.45-0.50$ at optimal tip-speed ratios.

\subsection{Power and Thrust Curves}

Real turbines have operational limits defined by cut-in ($u_{ci}$), rated ($u_r$), and cut-out ($u_{co}$) wind speeds. The power curve follows:

\begin{equation}
P(u) = \begin{cases}
0 & u < u_{ci} \\
\frac{1}{2}\rho A u^3 C_P(u) & u_{ci} \leq u < u_r \\
P_{rated} & u_r \leq u < u_{co} \\
0 & u \geq u_{co}
\end{cases}
\end{equation}

The thrust coefficient varies with wind speed and control strategy (pitch, yaw), typically peaking around $u = 8-10$ m/s before decreasing as blade pitch control limits loads at higher wind speeds.

\section{Wake Modeling Theory}

\subsection{Wake Structure}

The wake behind a wind turbine consists of two regions:
\begin{itemize}
    \item \textbf{Near wake} (0-3D downstream): Strong pressure gradients, tip vortices, and hub vortex dominate the flow.
    \item \textbf{Far wake} (>3D downstream): Pressure equilibrates, velocity deficit spreads radially, and turbulence intensity decreases.
\end{itemize}

Engineering wake models typically focus on the far wake, where simplified assumptions about velocity profiles are more valid.

\subsection{Conservation Principles}

Wake models are derived from conservation of mass and momentum. For a control volume encompassing the turbine and wake:

\textbf{Mass conservation:}
\begin{equation}
\rho u_\infty A_\infty = \rho u_w A_w
\end{equation}

\textbf{Momentum conservation:}
\begin{equation}
T = \rho A_d u_d (u_\infty - u_w)
\end{equation}

where subscripts $\infty$, $d$, and $w$ denote free-stream, disk, and wake quantities respectively.

\subsection{Jensen (Park) Model}

The Jensen model assumes a top-hat velocity profile with linear wake expansion:

\begin{equation}
r_w(x) = r_0 + kx
\end{equation}

where $r_0$ is the rotor radius, $x$ is downstream distance, and $k$ is the wake decay constant (typically 0.04-0.075 for onshore sites).

The velocity deficit is:

\begin{equation}
\frac{\Delta u}{u_\infty} = \frac{1 - \sqrt{1 - C_T}}{\left(1 + \frac{kx}{r_0}\right)^2}
\end{equation}

\subsection{Gaussian Wake Model}

The Gaussian model assumes a more realistic velocity deficit distribution:

\begin{equation}
\frac{\Delta u}{u_\infty} = \frac{1 - \sqrt{1 - C_T}}{8(\sigma_y/D)(\sigma_z/D)} \exp\left(-\frac{1}{2}\left(\frac{y}{\sigma_y}\right)^2 - \frac{1}{2}\left(\frac{z}{\sigma_z}\right)^2\right)
\end{equation}

where $\sigma_y$ and $\sigma_z$ are the wake widths in lateral and vertical directions, often modeled as:

\begin{equation}
\sigma_y = k_y x + \epsilon D, \quad \sigma_z = k_z x + \epsilon D
\end{equation}

with $k_y, k_z$ being expansion rates and $\epsilon$ accounting for the initial wake width.

\subsection{Wake Superposition}

Multiple wakes interact nonlinearly. Common superposition models include:

\textbf{Linear superposition:}
\begin{equation}
u = u_\infty - \sum_{i=1}^{N} \Delta u_i
\end{equation}

\textbf{Sum of squares (Katic):}
\begin{equation}
u = u_\infty \sqrt{1 - \sum_{i=1}^{N} \left(\frac{\Delta u_i}{u_\infty}\right)^2}
\end{equation}

\textbf{Energy balance:}
\begin{equation}
u = u_\infty \left(1 - \sqrt{\sum_{i=1}^{N} \left(1 - \frac{u_i}{u_\infty}\right)^2}\right)
\end{equation}

\section{Optimization Theory}

\subsection{Problem Formulation}

The general constrained optimization problem is:

\begin{equation}
\begin{aligned}
\min_{\mathbf{x} \in \mathbb{R}^n} & \quad f(\mathbf{x}) \\
\text{subject to} & \quad g_i(\mathbf{x}) \leq 0, \quad i = 1, \ldots, m \\
& \quad h_j(\mathbf{x}) = 0, \quad j = 1, \ldots, p \\
& \quad \mathbf{x}_L \leq \mathbf{x} \leq \mathbf{x}_U
\end{aligned}
\end{equation}

For wind farm optimization, $\mathbf{x} = [\mathbf{x}_1, \mathbf{y}_1, \ldots, \mathbf{x}_n, \mathbf{y}_n]^T$ represents turbine coordinates, $f(\mathbf{x}) = -\text{AEP}(\mathbf{x})$ is the negative annual energy production, and constraints include boundaries and spacing requirements.

\subsection{Optimality Conditions}

For differentiable problems, the Karush-Kuhn-Tucker (KKT) conditions provide necessary conditions for optimality:

\begin{equation}
\begin{aligned}
\nabla f(\mathbf{x}^*) + \sum_{i=1}^{m} \lambda_i \nabla g_i(\mathbf{x}^*) + \sum_{j=1}^{p} \mu_j \nabla h_j(\mathbf{x}^*) &= 0 \\
g_i(\mathbf{x}^*) &\leq 0, \quad i = 1, \ldots, m \\
h_j(\mathbf{x}^*) &= 0, \quad j = 1, \ldots, p \\
\lambda_i &\geq 0, \quad i = 1, \ldots, m \\
\lambda_i g_i(\mathbf{x}^*) &= 0, \quad i = 1, \ldots, m
\end{aligned}
\end{equation}

where $\lambda_i$ and $\mu_j$ are Lagrange multipliers.

\subsection{Evolutionary Algorithms}

Evolutionary algorithms operate on populations of solutions, using biologically-inspired operators:

\textbf{Selection:} Choose parents based on fitness:
\begin{equation}
P(\text{select } i) = \frac{f_i}{\sum_{j=1}^{N} f_j} \quad \text{(proportional selection)}
\end{equation}

\textbf{Crossover:} Combine parent solutions:
\begin{equation}
\mathbf{x}_{child} = \alpha \mathbf{x}_{parent1} + (1-\alpha) \mathbf{x}_{parent2}
\end{equation}

\textbf{Mutation:} Introduce random variations:
\begin{equation}
\mathbf{x}_{new} = \mathbf{x}_{old} + \sigma \mathcal{N}(0, 1)
\end{equation}

\subsection{Gradient-Based Methods}

Gradient methods use derivative information to find descent directions. The basic gradient descent update is:

\begin{equation}
\mathbf{x}_{k+1} = \mathbf{x}_k - \alpha_k \nabla f(\mathbf{x}_k)
\end{equation}

where $\alpha_k$ is the step size. More sophisticated methods include:

\textbf{Newton's method:}
\begin{equation}
\mathbf{x}_{k+1} = \mathbf{x}_k - \alpha_k \mathbf{H}_k^{-1} \nabla f(\mathbf{x}_k)
\end{equation}

where $\mathbf{H}_k$ is the Hessian matrix.

\textbf{Quasi-Newton methods (BFGS, L-BFGS):} Approximate the Hessian using gradient information:
\begin{equation}
\mathbf{H}_{k+1} = \mathbf{H}_k + \frac{\mathbf{y}_k \mathbf{y}_k^T}{\mathbf{y}_k^T \mathbf{s}_k} - \frac{\mathbf{H}_k \mathbf{s}_k \mathbf{s}_k^T \mathbf{H}_k}{\mathbf{s}_k^T \mathbf{H}_k \mathbf{s}_k}
\end{equation}

where $\mathbf{s}_k = \mathbf{x}_{k+1} - \mathbf{x}_k$ and $\mathbf{y}_k = \nabla f(\mathbf{x}_{k+1}) - \nabla f(\mathbf{x}_k)$.

\section{Automatic Differentiation}

\subsection{Forward Mode AD}

Forward mode AD computes derivatives alongside function values using dual numbers:

\begin{equation}
f(x + \epsilon) = f(x) + f'(x)\epsilon
\end{equation}

For a function $y = f(x_1, x_2, \ldots, x_n)$, forward mode computes:
\begin{equation}
\dot{y} = \sum_{i=1}^{n} \frac{\partial f}{\partial x_i} \dot{x}_i
\end{equation}

This requires $n$ forward passes to compute all partial derivatives.

\subsection{Reverse Mode AD}

Reverse mode AD computes derivatives by propagating adjoint values backward through the computational graph:

\begin{equation}
\bar{x}_i = \sum_{j \in \text{children}(i)} \bar{y}_j \frac{\partial y_j}{\partial x_i}
\end{equation}

where $\bar{x}_i = \frac{\partial L}{\partial x_i}$ for some scalar loss $L$. This requires only one forward and one backward pass regardless of input dimension.

\subsection{Implementation in JAX}

JAX implements reverse-mode AD through function transformation:

\begin{lstlisting}[language=Python]
import jax
import jax.numpy as jnp

def f(x):
    return jnp.sum(x**2)

# Gradient function
grad_f = jax.grad(f)

# Automatic differentiation
x = jnp.array([1.0, 2.0, 3.0])
gradient = grad_f(x)  # Returns [2.0, 4.0, 6.0]
\end{lstlisting}

\section{Differentiable Approximations}

\subsection{Smooth Approximations for Discontinuous Functions}

Non-differentiable components in wake models must be replaced with smooth approximations:

\textbf{Sigmoid function:} Replace step functions:
\begin{equation}
\text{step}(x) \approx \sigma(kx) = \frac{1}{1 + e^{-kx}}
\end{equation}

\textbf{Softplus:} Replace $\max(0, x)$:
\begin{equation}
\text{softplus}(x) = \frac{1}{\beta}\log(1 + e^{\beta x})
\end{equation}

\textbf{LogSumExp:} Replace $\max(x_1, x_2, \ldots, x_n)$:
\begin{equation}
\text{LSE}(x_1, \ldots, x_n) = \log\left(\sum_{i=1}^{n} e^{x_i}\right)
\end{equation}

\subsection{Smooth Power Curves}

Replace tabulated power curves with smooth functions:

\begin{equation}
P(u) = P_{rated} \cdot \sigma(k_1(u - u_{ci})) \cdot (1 - \sigma(k_2(u - u_{r}))) \cdot \phi(u)
\end{equation}

where $\phi(u)$ is a smooth function capturing the power rise:
\begin{equation}
\phi(u) = 3\left(\frac{u - u_{ci}}{u_r - u_{ci}}\right)^2 - 2\left(\frac{u - u_{ci}}{u_r - u_{ci}}\right)^3
\end{equation}

\section{Constraint Handling Methods}

\subsection{Penalty Methods}

Convert constrained problems to unconstrained by adding penalty terms:

\begin{equation}
f_p(\mathbf{x}) = f(\mathbf{x}) + \mu \sum_{i=1}^{m} \max(0, g_i(\mathbf{x}))^2
\end{equation}

where $\mu$ is the penalty parameter. As $\mu \to \infty$, solutions of the penalized problem approach feasible solutions.

\subsection{Barrier Methods}

Interior point methods use barrier functions to keep iterates strictly feasible:

\begin{equation}
f_b(\mathbf{x}) = f(\mathbf{x}) - \mu \sum_{i=1}^{m} \log(-g_i(\mathbf{x}))
\end{equation}

This requires an initially feasible point and maintains feasibility throughout optimization.

\subsection{Augmented Lagrangian Methods}

Combine penalty and Lagrangian approaches:

\begin{equation}
\mathcal{L}_A(\mathbf{x}, \boldsymbol{\lambda}, \mu) = f(\mathbf{x}) + \sum_{i=1}^{m} \lambda_i g_i(\mathbf{x}) + \frac{\mu}{2} \sum_{i=1}^{m} g_i(\mathbf{x})^2
\end{equation}

This provides better conditioning than pure penalty methods while handling inequality constraints.

\subsection{Constraint Handling in Evolutionary Algorithms}

Common strategies include:

\textbf{Death penalty:} Reject infeasible solutions (may lose diversity).

\textbf{Repair mechanisms:} Project infeasible solutions to feasible region:
\begin{equation}
\mathbf{x}_{repaired} = \arg\min_{\mathbf{y} \in \mathcal{F}} \|\mathbf{y} - \mathbf{x}\|_2
\end{equation}

\textbf{Constraint dominance:} In selection, feasible solutions always dominate infeasible ones:
\begin{equation}
\mathbf{x}_1 \prec \mathbf{x}_2 \iff 
\begin{cases}
v(\mathbf{x}_1) < v(\mathbf{x}_2) & \text{if both infeasible} \\
\text{true} & \text{if } \mathbf{x}_1 \text{ feasible, } \mathbf{x}_2 \text{ infeasible} \\
f(\mathbf{x}_1) < f(\mathbf{x}_2) & \text{if both feasible}
\end{cases}
\end{equation}

where $v(\mathbf{x})$ is the constraint violation.

\section{Hybrid Optimization Theory}

\subsection{Sequential Hybridization}

Sequential hybrid methods partition the optimization budget:

\begin{equation}
\mathbf{x}^* = \arg\min_{\mathbf{x}} f(\mathbf{x}) = \text{Local}(\text{Global}(\mathbf{x}_0, B_g), B_l)
\end{equation}

where $B_g$ and $B_l$ are global and local search budgets. Optimal budget allocation depends on problem characteristics:

\begin{equation}
B_g^* = \arg\max_{B_g} \mathbb{E}[f(\text{Local}(\text{Global}(\mathbf{x}_0, B_g), B - B_g))]
\end{equation}

\subsection{Interleaved Hybridization}

Alternate between global and local phases:

\begin{equation}
\mathbf{x}_{k+1} = 
\begin{cases}
\text{Global}(\mathbf{x}_k, b_g) & \text{if } k \bmod n = 0 \\
\text{Local}(\mathbf{x}_k, b_l) & \text{otherwise}
\end{cases}
\end{equation}

This allows information exchange between methods throughout optimization.

\subsection{Gradient-Informed Evolution}

Incorporate gradient information into evolutionary operators:

\textbf{Gradient-based mutation:}
\begin{equation}
\mathbf{x}_{mutated} = \mathbf{x} - \alpha \nabla f(\mathbf{x}) + \sigma \mathcal{N}(0, \mathbf{I})
\end{equation}

\textbf{Gradient-based crossover:}
\begin{equation}
\mathbf{x}_{child} = \mathbf{x}_{parent1} + \beta(\mathbf{x}_{parent2} - \mathbf{x}_{parent1}) - \gamma \nabla f(\mathbf{x}_{parent1})
\end{equation}

\section{Summary}

This chapter has established the theoretical foundations for wind farm layout optimization:

\begin{enumerate}
    \item \textbf{Wake physics:} From actuator disk theory to engineering wake models, providing the physical basis for power calculations.
    
    \item \textbf{Optimization theory:} Covering both evolutionary and gradient-based methods, with their respective strengths and limitations.
    
    \item \textbf{Automatic differentiation:} Explaining how modern AD enables efficient gradient computation for complex models.
    
    \item \textbf{Smooth approximations:} Showing how to make non-differentiable components amenable to gradient-based optimization.
    
    \item \textbf{Constraint handling:} Reviewing methods for both evolutionary and gradient-based optimizers.
    
    \item \textbf{Hybrid optimization:} Presenting theoretical frameworks for combining global and local search methods.
\end{enumerate}

These foundations enable the development of DifferentiableFLORIS (Chapter 4), the multi-framework optimization platform (Chapter 5), and hybrid optimization strategies (Chapter 6) that form the core contributions of this thesis.