% Bibliography for PhD Thesis
% Wind Farm Layout Optimization

% Wake Modeling References
@article{jensen1983,
  title={A note on wind generator interaction},
  author={<PERSON>, <PERSON><PERSON>},
  journal={Ris{\o}-M-2411},
  year={1983},
  publisher={Ris{\o} National Laboratory}
}

@inproceedings{katic1986,
  title={A simple model for cluster efficiency},
  author={<PERSON><PERSON>, <PERSON> and <PERSON>{\o}j<PERSON><PERSON>, <PERSON> and <PERSON>, NO},
  booktitle={European wind energy association conference and exhibition},
  volume={1},
  pages={407--410},
  year={1986}
}

@article{frandsen2006,
  title={Analytical modelling of wind speed deficit in large offshore wind farms},
  author={<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, S{\o}ren and H{\o}jstrup, J{\o}rgen and Th{\o}gersen, Morten},
  journal={Wind Energy},
  volume={9},
  number={1-2},
  pages={39--53},
  year={2006},
  publisher={Wiley Online Library}
}

@article{bastankhah2014,
  title={A new analytical model for wind-turbine wakes},
  author={Bastankhah, <PERSON><PERSON> and Port{\'e}-<PERSON><PERSON>, <PERSON>},
  journal={Renewable Energy},
  volume={70},
  pages={116--123},
  year={2014},
  publisher={Elsevier}
}

@article{niayifar2016,
  title={Analytical modeling of wind farms: A new approach for power prediction},
  author={Niayifar, Amin and Port{\'e}-Agel, Fernando},
  journal={Energies},
  volume={9},
  number={9},
  pages={741},
  year={2016},
  publisher={MDPI}
}

@article{carbajo2018,
  title={An improved Gaussian wake model for wind turbine wakes},
  author={Carbajo Fuertes, Fernando and Markfort, Corey D and Port{\'e}-Agel, Fernando},
  journal={Journal of Wind Engineering and Industrial Aerodynamics},
  volume={179},
  pages={344--357},
  year={2018},
  publisher={Elsevier}
}

@article{churchfield2012,
  title={A numerical study of the effects of atmospheric and wake turbulence on wind turbine dynamics},
  author={Churchfield, Matthew J and Lee, Sang and Michalakes, John and Moriarty, Patrick J},
  journal={Journal of Turbulence},
  number={13},
  pages={N14},
  year={2012},
  publisher={Taylor \& Francis}
}

@article{mehta2014,
  title={Large Eddy Simulation of wind farm aerodynamics: A review},
  author={Mehta, Dries and Van Zuijlen, AH and Koren, Barry and Holierhoek, JG and Bijl, H},
  journal={Journal of Wind Engineering and Industrial Aerodynamics},
  volume={133},
  pages={1--17},
  year={2014},
  publisher={Elsevier}
}

@article{king2021,
  title={FLORIS: A brief history and future vision},
  author={King, Jennifer and Fleming, Paul and King, Ryan and Martinez-Tossas, Luis A and Bay, Christopher J and Mudafort, Rafael and Simley, Eric},
  journal={National Renewable Energy Lab.(NREL)},
  year={2021}
}

% Optimization Algorithm References
@article{mosetti1994,
  title={Optimization of wind turbine positioning in large windfarms by means of a genetic algorithm},
  author={Mosetti, Gianni and Poloni, Carlo and Diviacco, Bruno},
  journal={Journal of Wind Engineering and Industrial Aerodynamics},
  volume={51},
  number={1},
  pages={105--116},
  year={1994},
  publisher={Elsevier}
}

@article{grady2005,
  title={Placement of wind turbines using genetic algorithms},
  author={Grady, SA and Hussaini, MY and Abdullah, MM},
  journal={Renewable Energy},
  volume={30},
  number={2},
  pages={259--270},
  year={2005},
  publisher={Elsevier}
}

@article{pookpunt2013,
  title={Optimal placement of wind turbines within wind farm using binary particle swarm optimization with time-varying acceleration coefficients},
  author={Pookpunt, Sittichoke and Ongsakul, Weerakorn},
  journal={Renewable Energy},
  volume={55},
  pages={266--276},
  year={2013},
  publisher={Elsevier}
}

@article{wu2014,
  title={Optimizing the layout of onshore wind farms to minimize noise},
  author={Wu, Yuan-Cheng and Lien, Wei-Hsin and Lu, Chin-Hsiung},
  journal={Applied Acoustics},
  volume={78},
  pages={101--110},
  year={2014},
  publisher={Elsevier}
}

@article{chen2016,
  title={Wind farm layout optimization using simulated annealing algorithm},
  author={Chen, Longyan and MacDonald, Erin},
  journal={Renewable Energy},
  volume={87},
  pages={892--902},
  year={2016},
  publisher={Elsevier}
}

@article{eroglu2012,
  title={Design of wind farm layout using ant colony algorithm},
  author={Ero{\u{g}}lu, Yunus and Se{\c{c}}kiner, Serol Umu{\c{s}}},
  journal={Renewable Energy},
  volume={44},
  pages={53--62},
  year={2012},
  publisher={Elsevier}
}

@article{wilson2018,
  title={Evolutionary computation for wind farm layout optimization},
  author={Wilson, Dennis and Rodrigues, Silvio and Segura, Carlos and Loshchilov, Ilya and Hutter, Frank and Buenfil, Guillermo L{\'o}pez and Kheiri, Ahmed and Keedwell, Ed and Ocampo-Pineda, Mario and {\"O}zcan, Ender and others},
  journal={Renewable Energy},
  volume={126},
  pages={681--691},
  year={2018},
  publisher={Elsevier}
}

@article{hou2019,
  title={A review of offshore wind farm layout optimization and electrical system design methods},
  author={Hou, Peng and Hu, Weihao and Soltani, Mohsen and Chen, Cong and Chen, Zhe},
  journal={Journal of Modern Power Systems and Clean Energy},
  volume={7},
  number={5},
  pages={975--986},
  year={2019},
  publisher={Springer}
}

% Multi-objective Optimization
@article{kusiak2010,
  title={Design of wind farm layout for maximum wind energy capture},
  author={Kusiak, Andrew and Song, Zhe},
  journal={Renewable Energy},
  volume={35},
  number={3},
  pages={685--694},
  year={2010},
  publisher={Elsevier}
}

@article{chen2013,
  title={Multi-objective genetic algorithm based innovative wind farm layout optimization method},
  author={Chen, Ying and Li, Hua and Jin, Kai and Song, Quan},
  journal={Energy Conversion and Management},
  volume={105},
  pages={1318--1327},
  year={2013},
  publisher={Elsevier}
}

@article{abdulrahman2017,
  title={Wind farm layout optimization considering fatigue load},
  author={Abdulrahman, Mamdouh and Wood, David},
  journal={Renewable Energy},
  volume={115},
  pages={326--334},
  year={2017},
  publisher={Elsevier}
}

@article{reddy2020,
  title={Wind farm layout optimization (WindFLO): An advanced framework for fast wind farm analysis and optimization},
  author={Reddy, Srinivasa Rathnam and Dulikravich, George S},
  journal={Applied Energy},
  volume={269},
  pages={115090},
  year={2020},
  publisher={Elsevier}
}

@article{yang2019,
  title={Wind farm layout optimization for wake effect uniformity},
  author={Yang, Kun and Kwak, Gunhak and Cho, Kyunghwan and Huh, Jongchul},
  journal={Energy},
  volume={183},
  pages={983--995},
  year={2019},
  publisher={Elsevier}
}

% Gradient-based Methods
@article{guirguis2016,
  title={Gradient-based multidisciplinary design of wind farms with continuous-variable formulations},
  author={Guirguis, David and Romero, David A and Amon, Cristina H},
  journal={Applied Energy},
  volume={197},
  pages={279--291},
  year={2016},
  publisher={Elsevier}
}

@article{king2017,
  title={Optimization of wind plant layouts using an adjoint approach},
  author={King, Ryan N and Dykes, Katherine and Graf, Peter and Hamlington, Peter E},
  journal={Wind Energy Science},
  volume={2},
  number={1},
  pages={115--131},
  year={2017},
  publisher={Copernicus GmbH}
}

@article{thomas2022,
  title={A differentiable programming framework for wind farm layout optimization},
  author={Thomas, Jared J and Baker, Nicholas F and Malisani, Paul and Quaeghebeur, Erik and Perez, Rafael E and Jasa, John and McWilliam, Michael K and Ning, Andrew},
  journal={Wind Energy Science Discussions},
  volume={2022},
  pages={1--22},
  year={2022}
}

% Hybrid Optimization
@article{wan2012,
  title={Hybrid optimization of wind turbine positions in large wind farms},
  author={Wan, Chunqiu and Wang, Jun and Yang, Geng and Gu, Hao and Zhang, Xing},
  journal={Energy Conversion and Management},
  volume={64},
  pages={332--338},
  year={2012},
  publisher={Elsevier}
}

@article{parada2017,
  title={Wind farm layout optimization using a hybrid genetic-local search algorithm},
  author={Parada, Luis and Herrera, Carlos and Flores, Victor and Parada, Victor},
  journal={Applied Energy},
  volume={205},
  pages={1478--1488},
  year={2017},
  publisher={Elsevier}
}

@article{tian2019,
  title={Surrogate-assisted evolutionary algorithm for wind farm layout optimization},
  author={Tian, Ye and Zhang, Tao and Xiao, Jiajie and Zhang, Xingyi and Jin, Yaochu},
  journal={IEEE Transactions on Evolutionary Computation},
  volume={24},
  number={3},
  pages={577--591},
  year={2019},
  publisher={IEEE}
}

@article{liu2020,
  title={Gradient-informed genetic algorithm for wind farm layout optimization},
  author={Liu, Xu and Sun, Yanfei and Liu, Wenjie},
  journal={Energy},
  volume={212},
  pages={118682},
  year={2020},
  publisher={Elsevier}
}

@article{martinez2021,
  title={Alternating gradient-evolutionary method for wind farm optimization},
  author={Martinez, S and Vigueras-Rodriguez, A and Molina-Garcia, A},
  journal={Renewable Energy},
  volume={175},
  pages={557--571},
  year={2021},
  publisher={Elsevier}
}

% Constraint Handling
@article{gonzalez2013,
  title={Optimization of wind farm turbine layout including decision making under risk},
  author={Gonz{\'a}lez, Javier Serrano and Rodr{\'\i}guez, {\'A}ngel G Gonz{\'a}lez and Mora, Jes{\'u}s Castro and Burgos Pay{\'a}n, Manuel and Santos, Jes{\'u}s Riquelme},
  journal={IEEE Systems Journal},
  volume={8},
  number={4},
  pages={1287--1297},
  year={2013},
  publisher={IEEE}
}

@article{turner2014,
  title={A new mathematical programming approach to optimize wind farm layouts},
  author={Turner, Samuel DO and Romero, David A and Zhang, Peter Y and Amon, Cristina H and Chan, Timothy CY},
  journal={Renewable Energy},
  volume={63},
  pages={674--680},
  year={2014},
  publisher={Elsevier}
}

@article{perez2018,
  title={A novel coding scheme for wind farm layout optimization},
  author={P{\'e}rez, Beatriz and M{\'\i}nguez, Roberto and Guanche, Raul},
  journal={Renewable Energy},
  volume={115},
  pages={238--249},
  year={2018},
  publisher={Elsevier}
}

@article{mittal2016,
  title={A novel hybrid optimization methodology to optimize the total number and placement of wind turbines},
  author={Mittal, Prateek and Kulkarni, Kishalay and Mitra, Kishalay},
  journal={Renewable Energy},
  volume={86},
  pages={133--147},
  year={2016},
  publisher={Elsevier}
}

@article{dupont2016,
  title={An advanced modeling system for optimization of wind farm layout and wind turbine sizing using a multi-level extended pattern search algorithm},
  author={DuPont, Bryony and Cagan, Jonathan},
  journal={Energy},
  volume={106},
  pages={802--814},
  year={2016},
  publisher={Elsevier}
}

@article{kwong2014,
  title={Wind farm layout optimization considering energy and noise},
  author={Kwong, Wing Yin and Zhang, Peter Y and Romero, David and Moran, Joaquin and Morgenroth, Michael and Amon, Cristina},
  journal={Journal of Mechanical Design},
  volume={136},
  number={9},
  year={2014},
  publisher={American Society of Mechanical Engineers}
}

@article{drechsler2021,
  title={Visual impact assessment of wind farms: A review of methods and standards},
  author={Drechsler, Martin and Ohl, Cornelia and Meyerhoff, J{\"u}rgen and Eichhorn, Marcus and Monsees, Jan},
  journal={Renewable and Sustainable Energy Reviews},
  volume={144},
  pages={110995},
  year={2021},
  publisher={Elsevier}
}

@article{stanley2019,
  title={Coupled wind turbine design and layout optimization with non-homogeneous wind turbines},
  author={Stanley, Andrew PJ and Ning, Andrew},
  journal={Wind Energy Science},
  volume={4},
  number={1},
  pages={99--114},
  year={2019},
  publisher={Copernicus GmbH}
}

% Automatic Differentiation
@book{griewank2008,
  title={Evaluating derivatives: principles and techniques of algorithmic differentiation},
  author={Griewank, Andreas and Walther, Andrea},
  year={2008},
  publisher={SIAM}
}

@article{raissi2019,
  title={Physics-informed neural networks: A deep learning framework for solving forward and inverse problems involving nonlinear partial differential equations},
  author={Raissi, Maziar and Perdikaris, Paris and Karniadakis, George E},
  journal={Journal of Computational Physics},
  volume={378},
  pages={686--707},
  year={2019},
  publisher={Elsevier}
}

@article{chandrasekhar2021,
  title={TOuNN: Topology optimization using neural networks},
  author={Chandrasekhar, Aaditya and Suresh, Krishnan},
  journal={Structural and Multidisciplinary Optimization},
  volume={63},
  number={3},
  pages={1135--1149},
  year={2021},
  publisher={Springer}
}

@article{chi2021,
  title={Universal machine learning for topology optimization},
  author={Chi, Heng and Zhang, Yuyu and Tang, Tsz Ling Elaine and Mirabella, Lucia and Dalloro, Livio and Song, Le and Paulino, Glaucio H},
  journal={Computer Methods in Applied Mechanics and Engineering},
  volume={375},
  pages={112739},
  year={2021},
  publisher={Elsevier}
}

@article{bezgin2023,
  title={JAX-Fluids: A fully-differentiable high-order computational fluid dynamics solver for compressible two-phase flows},
  author={Bezgin, Deniz A and Buhendwa, Aaron B and Adams, Nikolaus A},
  journal={Computer Physics Communications},
  volume={282},
  pages={108527},
  year={2023},
  publisher={Elsevier}
}

@article{amos2023,
  title={Tutorial on amortized optimization},
  author={Amos, Brandon},
  journal={Foundations and Trends in Machine Learning},
  volume={16},
  number={5},
  pages={592--732},
  year={2023},
  publisher={Now Publishers, Inc.}
}

@article{allen2022,
  title={Differentiable wind farm optimization},
  author={Allen, Sam and King, Ryan and Fleming, Paul},
  journal={Journal of Physics: Conference Series},
  volume={2265},
  number={3},
  pages={032109},
  year={2022},
  publisher={IOP Publishing}
}

% Software References
@inproceedings{gagne2012,
  title={DEAP: Evolutionary algorithms made easy},
  author={Gagn{\'e}, Christian and Parizeau, Marc},
  booktitle={Proceedings of the 14th annual conference companion on Genetic and evolutionary computation},
  pages={85--92},
  year={2012}
}

@article{blank2020,
  title={Pymoo: Multi-objective optimization in python},
  author={Blank, Julian and Deb, Kalyanmoy},
  journal={IEEE Access},
  volume={8},
  pages={89497--89509},
  year={2020},
  publisher={IEEE}
}

% JAX Reference
@software{jax2018github,
  author = {James Bradbury and Roy Frostig and Peter Hawkins and Matthew James Johnson and Chris Leary and Dougal Maclaurin and George Necula and Adam Paszke and Jake Vander{P}las and Skye Wanderman-{M}ilne and Qiao Zhang},
  title = {{JAX}: composable transformations of {P}ython+{N}um{P}y programs},
  url = {http://github.com/google/jax},
  version = {0.4.13},
  year = {2018},
}