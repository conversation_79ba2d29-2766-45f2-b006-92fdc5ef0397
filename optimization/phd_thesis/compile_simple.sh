#!/bin/bash
# Simple compilation script that ignores non-fatal errors

PDFLATEX="/prog/texlive/2013/bin/x86_64-linux/pdflatex"

echo "Compiling thesis (ignoring non-fatal errors)..."

# Run pdflatex with interaction=batchmode to suppress errors
$PDFLATEX -interaction=batchmode thesis.tex

# Check if PDF was created
if [ -f "thesis.pdf" ]; then
    echo "Success! PDF created despite errors."
    ls -lh thesis.pdf
else
    echo "Failed to create PDF. Checking log..."
    tail -50 thesis.log
fi