#!/usr/bin/env python3
"""
Generate figures for Chapter 6: Hybrid Gradient Optimization
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Circle, Rectangle, FancyBboxPatch, Polygon
from matplotlib.collections import PatchCollection
import seaborn as sns
from scipy.ndimage import gaussian_filter
import os

# Set style
plt.style.use('seaborn-v0_8-paper')
sns.set_palette("husl")

# Create figures directory
os.makedirs('figures', exist_ok=True)

def generate_optimization_landscape():
    """Generate a complex optimization landscape"""
    x = np.linspace(-5, 5, 200)
    y = np.linspace(-5, 5, 200)
    X, Y = np.meshgrid(x, y)
    
    # Create multi-modal landscape
    Z = np.zeros_like(X)
    
    # Add multiple Gaussian peaks (local optima)
    peaks = [
        (0, 0, 1.0, 1.0),      # Global optimum
        (-2, 2, 0.8, 0.8),     # Local optimum
        (2, -1, 0.7, 1.2),     # Local optimum
        (-1, -2, 0.6, 0.9),    # Local optimum
        (3, 3, 0.5, 1.1)       # Local optimum
    ]
    
    for cx, cy, amplitude, width in peaks:
        Z += amplitude * np.exp(-((X - cx)**2 + (Y - cy)**2) / (2 * width**2))
    
    # Add some noise and complexity
    noise = 0.05 * np.random.randn(*X.shape)
    Z += gaussian_filter(noise, sigma=2)
    
    # Add constraint boundaries (infeasible regions)
    constraint_mask = np.zeros_like(X, dtype=bool)
    constraint_mask[X**2 + Y**2 > 20] = True  # Circular boundary
    constraint_mask[np.abs(X) + np.abs(Y) < 1] = True  # Diamond exclusion
    
    Z[constraint_mask] = -1
    
    return X, Y, Z, constraint_mask

# Figure 1: Optimization Landscape
def plot_optimization_landscape():
    fig = plt.figure(figsize=(12, 10))
    
    X, Y, Z, constraint_mask = generate_optimization_landscape()
    
    # Main landscape plot
    ax1 = plt.subplot(2, 2, 1)
    
    # Create custom colormap for constraints
    Z_display = Z.copy()
    Z_display[constraint_mask] = np.nan
    
    contour = ax1.contourf(X, Y, Z_display, levels=20, cmap='viridis', alpha=0.8)
    ax1.contour(X, Y, Z_display, levels=10, colors='black', alpha=0.3, linewidths=0.5)
    
    # Mark optima
    ax1.plot(0, 0, 'w*', markersize=20, markeredgecolor='red', markeredgewidth=2, label='Global Optimum')
    ax1.plot([-2, 2, -1], [2, -1, -2], 'wo', markersize=12, markeredgecolor='orange', 
             markeredgewidth=2, label='Local Optima')
    
    # Show constraints
    ax1.contour(X, Y, constraint_mask.astype(float), levels=[0.5], colors='red', 
                linewidths=2, linestyles='--', alpha=0.8)
    ax1.fill(X[constraint_mask], Y[constraint_mask], 'red', alpha=0.2)
    
    ax1.set_xlabel('x₁')
    ax1.set_ylabel('x₂')
    ax1.set_title('Wind Farm Optimization Landscape')
    ax1.legend(loc='upper right')
    ax1.set_aspect('equal')
    
    # Gradient flow
    ax2 = plt.subplot(2, 2, 2)
    
    # Compute gradients
    dy, dx = np.gradient(Z_display)
    
    # Subsample for visibility
    skip = 8
    X_sub = X[::skip, ::skip]
    Y_sub = Y[::skip, ::skip]
    dx_sub = dx[::skip, ::skip]
    dy_sub = dy[::skip, ::skip]
    
    # Normalize gradients
    magnitude = np.sqrt(dx_sub**2 + dy_sub**2)
    dx_norm = dx_sub / (magnitude + 1e-8)
    dy_norm = dy_sub / (magnitude + 1e-8)
    
    # Plot gradient field
    ax2.contourf(X, Y, Z_display, levels=20, cmap='viridis', alpha=0.3)
    ax2.quiver(X_sub, Y_sub, -dx_norm, -dy_norm, magnitude, 
               cmap='plasma', scale=30, alpha=0.7)
    
    ax2.set_xlabel('x₁')
    ax2.set_ylabel('x₂')
    ax2.set_title('Gradient Flow Field')
    ax2.set_aspect('equal')
    
    # Cross-section view
    ax3 = plt.subplot(2, 2, 3)
    
    # Extract cross-section along y=0
    y_values = Y[:, 0]  # Get y values from first column
    idx_y0 = np.argmin(np.abs(y_values))
    Z_cross = Z[idx_y0, :]
    x_values = X[0, :]  # Get x values from first row
    
    ax3.plot(x_values, Z_cross, 'b-', linewidth=2)
    ax3.fill_between(x_values, -1, Z_cross, where=(Z_cross >= -0.5), alpha=0.3, color='blue')
    ax3.fill_between(x_values, -1, 0, where=(Z_cross < -0.5), alpha=0.3, color='red', label='Infeasible')
    
    ax3.set_xlabel('x₁')
    ax3.set_ylabel('Objective Value')
    ax3.set_title('Cross-section at x₂ = 0')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # Convergence basins
    ax4 = plt.subplot(2, 2, 4)
    
    # Simulate convergence basins
    basin_map = np.zeros_like(X)
    
    # Simple basin assignment based on nearest peak
    for i in range(X.shape[0]):
        for j in range(X.shape[1]):
            if constraint_mask[i, j]:
                basin_map[i, j] = -1
            else:
                distances = []
                for cx, cy, _, _ in [(0, 0, 1.0, 1.0), (-2, 2, 0.8, 0.8), 
                                    (2, -1, 0.7, 1.2), (-1, -2, 0.6, 0.9)]:
                    dist = np.sqrt((X[i, j] - cx)**2 + (Y[i, j] - cy)**2)
                    distances.append(dist)
                basin_map[i, j] = np.argmin(distances)
    
    # Plot basins
    cmap = plt.cm.get_cmap('tab10')
    basin_display = basin_map.copy()
    basin_display[basin_map == -1] = np.nan
    
    ax4.contourf(X, Y, basin_display, levels=[-0.5, 0.5, 1.5, 2.5, 3.5], 
                 cmap=cmap, alpha=0.7)
    ax4.contour(X, Y, constraint_mask.astype(float), levels=[0.5], 
                colors='red', linewidths=2, linestyles='--')
    
    ax4.set_xlabel('x₁')
    ax4.set_ylabel('x₂')
    ax4.set_title('Convergence Basins')
    ax4.set_aspect('equal')
    
    plt.tight_layout()
    plt.savefig('figures/optimization_landscape.pdf', dpi=300)
    plt.close()

# Figure 2: Hybrid Performance Comparison
def plot_hybrid_performance():
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Solution quality vs budget
    ax = axes[0, 0]
    
    budgets = np.logspace(2, 4, 50)
    
    # Simulated performance curves
    pure_evo = 0.95 - 0.3 * np.exp(-budgets / 2000)
    pure_grad = 0.85 - 0.5 * np.exp(-budgets / 500) + 0.1 * np.random.randn(len(budgets)) * 0.01
    sequential = 0.98 - 0.4 * np.exp(-budgets / 1500)
    interleaved = 0.97 - 0.35 * np.exp(-budgets / 1800)
    gradient_informed = 0.96 - 0.32 * np.exp(-budgets / 1600)
    
    ax.semilogx(budgets, pure_evo, 'b-', linewidth=2, label='Pure Evolutionary')
    ax.semilogx(budgets, pure_grad, 'r-', linewidth=2, label='Pure Gradient')
    ax.semilogx(budgets, sequential, 'g--', linewidth=2, label='Sequential Hybrid')
    ax.semilogx(budgets, interleaved, 'm--', linewidth=2, label='Interleaved Hybrid')
    ax.semilogx(budgets, gradient_informed, 'c--', linewidth=2, label='Gradient-Informed')
    
    ax.set_xlabel('Computational Budget (evaluations)')
    ax.set_ylabel('Solution Quality (normalized AEP)')
    ax.set_title('Solution Quality vs Budget')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim([0.5, 1.0])
    
    # Convergence profiles
    ax = axes[0, 1]
    
    generations = np.arange(0, 100)
    
    # Different convergence patterns
    evo_conv = 0.6 + 0.35 * (1 - np.exp(-generations / 30))
    grad_conv = np.concatenate([
        0.6 + 0.25 * (1 - np.exp(-generations[:20] / 5)),
        np.ones(80) * 0.85
    ])
    seq_conv = np.concatenate([
        0.6 + 0.25 * (1 - np.exp(-generations[:60] / 30)),
        0.85 + 0.13 * (1 - np.exp(-(generations[60:] - 60) / 10))
    ])
    
    ax.plot(generations, evo_conv, 'b-', linewidth=2, label='Evolutionary')
    ax.plot(generations, grad_conv, 'r-', linewidth=2, label='Gradient')
    ax.plot(generations, seq_conv, 'g-', linewidth=2, label='Sequential Hybrid')
    
    ax.axvline(x=60, color='gray', linestyle=':', alpha=0.5)
    ax.text(62, 0.7, 'Switch to\nGradient', fontsize=10, va='center')
    
    ax.set_xlabel('Generation / Iteration')
    ax.set_ylabel('Best Fitness')
    ax.set_title('Convergence Profiles')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Constraint satisfaction
    ax = axes[1, 0]
    
    methods = ['Pure\nEvo', 'Pure\nGrad', 'Sequential', 'Interleaved', 'Gradient\nInformed']
    spacing_rates = [0.92, 0.75, 0.96, 0.95, 0.94]
    boundary_rates = [0.98, 0.82, 0.99, 0.98, 0.97]
    
    x = np.arange(len(methods))
    width = 0.35
    
    ax.bar(x - width/2, spacing_rates, width, label='Spacing Constraints', alpha=0.7)
    ax.bar(x + width/2, boundary_rates, width, label='Boundary Constraints', alpha=0.7)
    
    ax.set_ylabel('Satisfaction Rate')
    ax.set_title('Constraint Satisfaction Rates')
    ax.set_xticks(x)
    ax.set_xticklabels(methods)
    ax.legend()
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_ylim([0.6, 1.05])
    
    # Final layout quality distribution
    ax = axes[1, 1]
    
    # Generate sample distributions
    np.random.seed(42)
    n_runs = 50
    
    pure_evo_dist = np.random.normal(0.93, 0.03, n_runs)
    pure_grad_dist = np.random.normal(0.85, 0.05, n_runs)
    sequential_dist = np.random.normal(0.97, 0.02, n_runs)
    interleaved_dist = np.random.normal(0.96, 0.025, n_runs)
    gradient_informed_dist = np.random.normal(0.95, 0.022, n_runs)
    
    data = [pure_evo_dist, pure_grad_dist, sequential_dist, 
            interleaved_dist, gradient_informed_dist]
    
    bp = ax.boxplot(data, labels=['Pure\nEvo', 'Pure\nGrad', 'Sequential', 
                                   'Interleaved', 'Gradient\nInformed'],
                    patch_artist=True)
    
    colors = ['lightblue', 'lightcoral', 'lightgreen', 'plum', 'lightcyan']
    for patch, color in zip(bp['boxes'], colors):
        patch.set_facecolor(color)
    
    ax.set_ylabel('Final AEP (normalized)')
    ax.set_title('Solution Quality Distribution (50 runs)')
    ax.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('figures/hybrid_performance_comparison.pdf', dpi=300)
    plt.close()

# Figure 3: Budget Allocation Analysis
def plot_budget_allocation():
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Optimal allocation vs problem complexity
    ax = axes[0]
    
    n_turbines = np.array([10, 25, 50, 100, 200])
    complexity = np.log(n_turbines) / np.log(100)
    
    # Different constraint scenarios
    loose_constraints = 0.5 + 0.35 * complexity
    medium_constraints = 0.55 + 0.30 * complexity
    tight_constraints = 0.65 + 0.20 * complexity
    
    ax.plot(n_turbines, loose_constraints, 'g-o', linewidth=2, markersize=8, 
            label='Loose Constraints')
    ax.plot(n_turbines, medium_constraints, 'b-s', linewidth=2, markersize=8, 
            label='Medium Constraints')
    ax.plot(n_turbines, tight_constraints, 'r-^', linewidth=2, markersize=8, 
            label='Tight Constraints')
    
    ax.set_xlabel('Number of Turbines')
    ax.set_ylabel('Optimal Evolutionary Allocation (α)')
    ax.set_title('Optimal Budget Allocation')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_xscale('log')
    ax.set_ylim([0.4, 0.9])
    
    # Allocation sensitivity
    ax = axes[1]
    
    allocations = np.linspace(0.3, 0.9, 50)
    
    # Performance for different problem sizes
    small_perf = 0.85 + 0.1 * np.sin(5 * (allocations - 0.5)) * np.exp(-2 * (allocations - 0.5)**2)
    medium_perf = 0.88 + 0.08 * np.sin(4 * (allocations - 0.6)) * np.exp(-3 * (allocations - 0.6)**2)
    large_perf = 0.90 + 0.06 * np.sin(3 * (allocations - 0.7)) * np.exp(-4 * (allocations - 0.7)**2)
    
    ax.plot(allocations, small_perf, 'g-', linewidth=2, label='10 Turbines')
    ax.plot(allocations, medium_perf, 'b-', linewidth=2, label='50 Turbines')
    ax.plot(allocations, large_perf, 'r-', linewidth=2, label='100 Turbines')
    
    # Mark optima
    ax.plot(0.5, small_perf[np.argmin(np.abs(allocations - 0.5))], 'go', markersize=10)
    ax.plot(0.6, medium_perf[np.argmin(np.abs(allocations - 0.6))], 'bo', markersize=10)
    ax.plot(0.7, large_perf[np.argmin(np.abs(allocations - 0.7))], 'ro', markersize=10)
    
    ax.set_xlabel('Evolutionary Budget Allocation (α)')
    ax.set_ylabel('Final Solution Quality')
    ax.set_title('Performance Sensitivity to Allocation')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_xlim([0.3, 0.9])
    
    plt.tight_layout()
    plt.savefig('figures/budget_allocation_analysis.pdf', dpi=300)
    plt.close()

# Figure 4: Gradient-Informed Operators
def plot_gradient_operators():
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # Gradient-biased mutation
    ax = axes[0, 0]
    
    # Generate sample mutations
    np.random.seed(42)
    theta = np.linspace(0, 2*np.pi, 100)
    
    # Standard Gaussian mutation
    r_standard = np.abs(np.random.normal(0, 1, 100))
    x_standard = r_standard * np.cos(theta)
    y_standard = r_standard * np.sin(theta)
    
    # Gradient direction (example: northeast)
    grad_dir = np.array([1, 1]) / np.sqrt(2)
    
    # Gradient-biased mutation
    bias_strength = 0.7
    r_biased = np.abs(np.random.normal(0, 1, 100))
    angles_biased = theta + bias_strength * np.cos(theta - np.arctan2(grad_dir[1], grad_dir[0]))
    x_biased = r_biased * np.cos(angles_biased)
    y_biased = r_biased * np.sin(angles_biased)
    
    ax.scatter(x_standard, y_standard, alpha=0.3, s=20, c='blue', label='Standard Mutation')
    ax.scatter(x_biased, y_biased, alpha=0.3, s=20, c='red', label='Gradient-Biased')
    ax.arrow(0, 0, 2*grad_dir[0], 2*grad_dir[1], head_width=0.2, head_length=0.2, 
             fc='green', ec='green', linewidth=2, label='Gradient Direction')
    
    ax.set_xlabel('Δx₁')
    ax.set_ylabel('Δx₂')
    ax.set_title('Gradient-Biased Mutation')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    ax.set_xlim([-3, 3])
    ax.set_ylim([-3, 3])
    
    # Gradient crossover
    ax = axes[0, 1]
    
    # Parent positions
    p1 = np.array([-2, -1])
    p2 = np.array([2, 1])
    
    # Gradient at midpoint
    midpoint = 0.5 * (p1 + p2)
    grad_mid = np.array([0.5, -0.8])  # Example gradient
    
    # Standard crossover points
    t_standard = np.linspace(0, 1, 11)
    standard_points = [p1 + t * (p2 - p1) for t in t_standard]
    
    # Gradient-informed crossover
    direction = -grad_mid / np.linalg.norm(grad_mid)
    t_gradient = np.linspace(-0.5, 1.5, 11)
    gradient_points = [midpoint + t * 2 * direction for t in t_gradient]
    
    # Plot
    ax.plot([p[0] for p in standard_points], [p[1] for p in standard_points], 
            'bo-', alpha=0.5, linewidth=2, markersize=6, label='Standard Crossover')
    ax.plot([p[0] for p in gradient_points], [p[1] for p in gradient_points], 
            'ro-', alpha=0.5, linewidth=2, markersize=6, label='Gradient Crossover')
    
    ax.plot(p1[0], p1[1], 'ks', markersize=12, label='Parent 1')
    ax.plot(p2[0], p2[1], 'k^', markersize=12, label='Parent 2')
    ax.arrow(midpoint[0], midpoint[1], -grad_mid[0], -grad_mid[1], 
             head_width=0.2, head_length=0.2, fc='green', ec='green', linewidth=2)
    
    ax.set_xlabel('x₁')
    ax.set_ylabel('x₂')
    ax.set_title('Gradient-Informed Crossover')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    
    # Local search probability
    ax = axes[1, 0]
    
    generations = np.arange(0, 200)
    
    # Base probability
    base_prob = 0.1 * (1 + generations / 100)
    
    # Success-adjusted probability
    success_history = np.random.binomial(1, 0.3 + 0.4 * (1 - np.exp(-generations / 50)), len(generations))
    success_rate = np.convolve(success_history, np.ones(20)/20, mode='same')
    adjusted_prob = base_prob * (1 + success_rate)
    adjusted_prob = np.minimum(adjusted_prob, 0.5)
    
    ax.plot(generations, base_prob, 'b-', linewidth=2, label='Base Probability')
    ax.plot(generations, adjusted_prob, 'r-', linewidth=2, label='Success-Adjusted')
    ax.fill_between(generations, 0, success_rate * 0.2, alpha=0.2, color='green', label='Success Rate')
    
    ax.set_xlabel('Generation')
    ax.set_ylabel('Local Search Probability')
    ax.set_title('Adaptive Local Search Probability')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_ylim([0, 0.6])
    
    # Information flow diagram
    ax = axes[1, 1]
    
    # Create flow diagram
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    
    # Evolutionary component
    evo_box = FancyBboxPatch((1, 6), 3, 2, boxstyle="round,pad=0.1", 
                             facecolor='lightblue', edgecolor='blue', linewidth=2)
    ax.add_patch(evo_box)
    ax.text(2.5, 7, 'Evolutionary\nAlgorithm', ha='center', va='center', fontsize=10, weight='bold')
    
    # Gradient component
    grad_box = FancyBboxPatch((6, 6), 3, 2, boxstyle="round,pad=0.1", 
                              facecolor='lightcoral', edgecolor='red', linewidth=2)
    ax.add_patch(grad_box)
    ax.text(7.5, 7, 'Gradient\nOptimization', ha='center', va='center', fontsize=10, weight='bold')
    
    # Population
    pop_box = FancyBboxPatch((1, 2), 3, 2, boxstyle="round,pad=0.1", 
                             facecolor='lightgreen', edgecolor='green', linewidth=2)
    ax.add_patch(pop_box)
    ax.text(2.5, 3, 'Population', ha='center', va='center', fontsize=10, weight='bold')
    
    # Gradient cache
    cache_box = FancyBboxPatch((6, 2), 3, 2, boxstyle="round,pad=0.1", 
                               facecolor='lightyellow', edgecolor='orange', linewidth=2)
    ax.add_patch(cache_box)
    ax.text(7.5, 3, 'Gradient\nCache', ha='center', va='center', fontsize=10, weight='bold')
    
    # Arrows
    # Evo to population
    ax.annotate('', xy=(2.5, 5.9), xytext=(2.5, 6),
                arrowprops=dict(arrowstyle='->', lw=2, color='blue'))
    # Population to evo
    ax.annotate('', xy=(2.5, 6), xytext=(2.5, 4.1),
                arrowprops=dict(arrowstyle='->', lw=2, color='green'))
    # Grad to cache
    ax.annotate('', xy=(7.5, 5.9), xytext=(7.5, 6),
                arrowprops=dict(arrowstyle='->', lw=2, color='red'))
    # Cache to grad
    ax.annotate('', xy=(7.5, 6), xytext=(7.5, 4.1),
                arrowprops=dict(arrowstyle='->', lw=2, color='orange'))
    # Population to grad
    ax.annotate('', xy=(6, 7), xytext=(4.1, 3),
                arrowprops=dict(arrowstyle='->', lw=2, color='purple', connectionstyle="arc3,rad=.3"))
    # Grad to population
    ax.annotate('', xy=(4.1, 3), xytext=(6, 7),
                arrowprops=dict(arrowstyle='->', lw=2, color='purple', connectionstyle="arc3,rad=-.3"))
    
    ax.text(5, 5.5, 'Elite\nSelection', ha='center', va='center', fontsize=9, 
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    ax.text(5, 4.5, 'Solution\nInjection', ha='center', va='center', fontsize=9,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
    
    ax.set_title('Gradient-Informed Evolution: Information Flow')
    ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('figures/gradient_informed_operators.pdf', dpi=300)
    plt.close()

# Figure 5: Constraint Handling Comparison
def plot_constraint_handling():
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Alternating projections
    ax = axes[0]
    
    # Initial infeasible point
    x0 = np.array([3, 3])
    
    # Constraints: circle and box
    theta = np.linspace(0, 2*np.pi, 100)
    circle_x = 2 * np.cos(theta)
    circle_y = 2 * np.sin(theta)
    
    box_x = [-1, 1, 1, -1, -1]
    box_y = [-1, -1, 1, 1, -1]
    
    # Projection iterations
    iterations = [x0]
    x_current = x0.copy()
    
    for i in range(5):
        # Project onto circle
        norm = np.linalg.norm(x_current)
        if norm > 2:
            x_current = x_current * (2 / norm)
        iterations.append(x_current.copy())
        
        # Project onto box
        x_current = np.clip(x_current, -1, 1)
        iterations.append(x_current.copy())
    
    # Plot
    ax.plot(circle_x, circle_y, 'b-', linewidth=2, label='Boundary Constraint')
    ax.plot(box_x, box_y, 'r-', linewidth=2, label='Spacing Constraint')
    ax.fill(circle_x, circle_y, 'blue', alpha=0.1)
    ax.fill(box_x, box_y, 'red', alpha=0.1)
    
    # Plot projection path
    for i in range(len(iterations)-1):
        ax.plot([iterations[i][0], iterations[i+1][0]], 
                [iterations[i][1], iterations[i+1][1]], 
                'ko-', markersize=6, linewidth=1.5, alpha=0.7)
        if i == 0:
            ax.plot(iterations[i][0], iterations[i][1], 'go', markersize=10, label='Start')
    
    ax.plot(iterations[-1][0], iterations[-1][1], 'ro', markersize=10, label='Converged')
    
    ax.set_xlabel('x₁')
    ax.set_ylabel('x₂')
    ax.set_title('Alternating Projections')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    ax.set_xlim([-3.5, 3.5])
    ax.set_ylim([-3.5, 3.5])
    
    # Augmented Lagrangian
    ax = axes[1]
    
    x = np.linspace(-2, 3, 100)
    
    # Original objective
    f = 0.1 * x**2 - 0.5 * x + 1
    
    # Constraint g(x) = x - 1 <= 0
    g = x - 1
    
    # Augmented Lagrangian for different mu
    mu_values = [1, 5, 20]
    lambda_val = 2
    
    for mu in mu_values:
        L_aug = f.copy()
        for i in range(len(x)):
            if g[i] > -lambda_val / (2 * mu):
                L_aug[i] += lambda_val * g[i] + 0.5 * mu * g[i]**2
            else:
                L_aug[i] -= 0.5 * lambda_val**2 / mu
        
        ax.plot(x, L_aug, linewidth=2, label=f'μ = {mu}')
    
    ax.plot(x, f, 'k--', linewidth=2, alpha=0.5, label='Original f(x)')
    ax.axvline(x=1, color='red', linestyle=':', linewidth=2, alpha=0.7, label='Constraint')
    ax.fill_betweenx([ax.get_ylim()[0], ax.get_ylim()[1]], 1, 3, alpha=0.2, color='red')
    
    ax.set_xlabel('x')
    ax.set_ylabel('Augmented Objective')
    ax.set_title('Augmented Lagrangian Method')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Gradient projection
    ax = axes[2]
    
    # Constraint boundary
    theta = np.linspace(0, 2*np.pi, 100)
    boundary_x = 2 + 0.5 * np.cos(theta) + 0.3 * np.cos(3*theta)
    boundary_y = 2 + 0.5 * np.sin(theta) + 0.3 * np.sin(3*theta)
    
    ax.fill(boundary_x, boundary_y, 'lightblue', alpha=0.3)
    ax.plot(boundary_x, boundary_y, 'b-', linewidth=2)
    
    # Point on boundary
    t = 0.3
    x_point = 2 + 0.5 * np.cos(2*np.pi*t) + 0.3 * np.cos(3*2*np.pi*t)
    y_point = 2 + 0.5 * np.sin(2*np.pi*t) + 0.3 * np.sin(3*2*np.pi*t)
    
    # Gradient and projected gradient
    grad = np.array([-1.5, -1.0])  # Example gradient
    
    # Normal vector (approximate)
    dt = 0.01
    x_next = 2 + 0.5 * np.cos(2*np.pi*(t+dt)) + 0.3 * np.cos(3*2*np.pi*(t+dt))
    y_next = 2 + 0.5 * np.sin(2*np.pi*(t+dt)) + 0.3 * np.sin(3*2*np.pi*(t+dt))
    tangent = np.array([x_next - x_point, y_next - y_point])
    tangent = tangent / np.linalg.norm(tangent)
    normal = np.array([-tangent[1], tangent[0]])
    
    # Project gradient
    grad_proj = grad - np.dot(grad, normal) * normal
    
    # Plot
    ax.plot(x_point, y_point, 'ko', markersize=10)
    ax.arrow(x_point, y_point, grad[0]*0.3, grad[1]*0.3, 
             head_width=0.05, head_length=0.05, fc='red', ec='red', 
             linewidth=2, alpha=0.7, label='Original Gradient')
    ax.arrow(x_point, y_point, grad_proj[0]*0.3, grad_proj[1]*0.3, 
             head_width=0.05, head_length=0.05, fc='green', ec='green', 
             linewidth=2, label='Projected Gradient')
    ax.arrow(x_point, y_point, normal[0]*0.3, normal[1]*0.3, 
             head_width=0.05, head_length=0.05, fc='blue', ec='blue', 
             linewidth=2, alpha=0.5, label='Normal Vector')
    
    ax.set_xlabel('x₁')
    ax.set_ylabel('x₂')
    ax.set_title('Gradient Projection on Constraints')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    ax.set_xlim([1, 3])
    ax.set_ylim([1, 3])
    
    plt.tight_layout()
    plt.savefig('figures/constraint_handling_methods.pdf', dpi=300)
    plt.close()

# Generate all figures
if __name__ == "__main__":
    print("Generating Chapter 6 figures...")
    
    plot_optimization_landscape()
    print("✓ Optimization landscape")
    
    plot_hybrid_performance()
    print("✓ Hybrid performance comparison")
    
    plot_budget_allocation()
    print("✓ Budget allocation analysis")
    
    plot_gradient_operators()
    print("✓ Gradient-informed operators")
    
    plot_constraint_handling()
    print("✓ Constraint handling methods")
    
    print("\nAll figures generated successfully!")