% PhD Thesis: Multi-Framework Wind Farm Layout Optimization with Differentiable Flow Models
% Author: Cherif <PERSON>
% Date: 2025

\documentclass[12pt,a4paper,oneside]{report}

% Essential packages
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[english]{babel}
\usepackage{amsmath,amssymb,amsthm}
\usepackage{graphicx}
\usepackage{subfigure}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{listings}
\usepackage{color}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{cite}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{array}
\usepackage{float}
\usepackage{geometry}
\usepackage{setspace}
\usepackage{fancyhdr}
\usepackage{titlesec}
% \usepackage{tocloft} % Commented out due to conflict with subfigure
\usepackage{appendix}
\usepackage{nomencl}
\usepackage{siunitx}

% Page geometry
\geometry{
    a4paper,
    left=3cm,
    right=2.5cm,
    top=2.5cm,
    bottom=2.5cm
}

% Line spacing
\onehalfspacing

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\fancyhead[R]{\thepage}
\fancyhead[L]{\leftmark}
\renewcommand{\headrulewidth}{0.4pt}

% Chapter title formatting
\titleformat{\chapter}[display]
{\normalfont\huge\bfseries}{\chaptertitlename\ \thechapter}{20pt}{\Huge}

% Code listing style
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}

\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},   
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,         
    breaklines=true,                 
    captionpos=b,                    
    keepspaces=true,                 
    numbers=left,                    
    numbersep=5pt,                  
    showspaces=false,                
    showstringspaces=false,
    showtabs=false,                  
    tabsize=2
}

\lstset{style=mystyle}

% Hyperref setup
\hypersetup{
    colorlinks=true,
    linkcolor=blue,
    filecolor=magenta,      
    urlcolor=cyan,
    citecolor=blue,
    pdftitle={Multi-Framework Wind Farm Layout Optimization},
    pdfauthor={Cherif Mihoubi},
}

% Theorem environments
\theoremstyle{definition}
\newtheorem{definition}{Definition}[chapter]
\newtheorem{theorem}{Theorem}[chapter]
\newtheorem{lemma}{Lemma}[chapter]
\newtheorem{corollary}{Corollary}[chapter]
\newtheorem{proposition}{Proposition}[chapter]

\theoremstyle{remark}
\newtheorem{remark}{Remark}[chapter]
\newtheorem{note}{Note}[chapter]

% Custom commands
\newcommand{\floris}{\textsc{FLORIS}}
\newcommand{\deap}{\textsc{DEAP}}
\newcommand{\pymoo}{\textsc{PyMoo}}
\newcommand{\nevergrad}{\textsc{Nevergrad}}
\newcommand{\jax}{\textsc{JAX}}

% Document properties
\title{Multi-Framework Wind Farm Layout Optimization with Differentiable Flow Models}
\author{Cherif Mihoubi}
\date{2025}

\begin{document}

% Title page
\begin{titlepage}
    \centering
    \vspace*{2cm}
    
    \Huge
    \textbf{Multi-Framework Wind Farm Layout Optimization with Differentiable Flow Models}
    
    \vspace{2cm}
    
    \Large
    A Thesis Submitted for the Degree of\\
    Doctor of Philosophy
    
    \vspace{2cm}
    
    by
    
    \vspace{1cm}
    
    \textbf{Cherif Mihoubi}
    
    \vfill
    
    Department of Engineering\\
    University Name\\
    2025
    
\end{titlepage}

% Abstract
\pagenumbering{roman}
\chapter*{Abstract}
\addcontentsline{toc}{chapter}{Abstract}

Wind farm layout optimization is a critical challenge in renewable energy, requiring the optimal placement of turbines to maximize energy production while respecting physical and operational constraints. This thesis presents a comprehensive multi-framework approach to wind farm layout optimization, introducing several novel contributions to the field.

First, we develop DifferentiableFLORIS, a \jax{}-based differentiable implementation of the FLOw Redirection and Induction in Steady-state (\floris{}) wake model. This implementation enables analytical gradient computation without modifying the original \floris{} codebase, using smooth approximations for traditionally non-differentiable components such as power curves and conditional wake interactions.

Second, we present a unified optimization framework that integrates three major evolutionary computation libraries: \deap{} (Distributed Evolutionary Algorithms in Python), \pymoo{} (Multi-objective Optimization in Python), and \nevergrad{} (Facebook's gradient-free optimization platform). This integration enables systematic comparison of diverse optimization algorithms under consistent conditions.

Third, we introduce hybrid optimization strategies that combine the global search capabilities of evolutionary algorithms with the local refinement power of gradient-based methods. Our HybridGradientOptimizer implements multiple strategies including sequential, interleaved, and gradient-informed approaches.

We present implementation results and honestly document both successes and challenges. While all frameworks successfully import and run basic optimizations, achieving feasible solutions with proper constraint satisfaction proved challenging. The work establishes a foundation for future development, with DifferentiableFLORIS demonstrating the feasibility of automatic differentiation for wake models and the multi-framework platform revealing important insights about integration complexity.

This work contributes to the growing field of differentiable programming in engineering optimization and provides practitioners with a flexible, extensible framework for wind farm layout optimization. All code is made available as open-source software to facilitate reproducibility and future research.

% Acknowledgements
\chapter*{Acknowledgements}
\addcontentsline{toc}{chapter}{Acknowledgements}

I would like to express my sincere gratitude to all those who have supported me throughout this research journey.

First and foremost, I thank my advisors for their invaluable guidance, patience, and encouragement. Their expertise and insights have been instrumental in shaping this work.

I am grateful to the developers and maintainers of the open-source libraries used in this research, including \floris{}, \deap{}, \pymoo{}, \nevergrad{}, and \jax{}. Their contributions to the scientific computing community have made this work possible.

Special thanks to my colleagues and fellow researchers who provided feedback, suggestions, and engaging discussions that helped refine the ideas presented in this thesis.

Finally, I thank my family and friends for their unwavering support and understanding throughout this challenging but rewarding journey.

% Table of contents
\tableofcontents
\listoffigures
\listoftables
\listofalgorithms

% Nomenclature
\chapter*{Nomenclature}
\addcontentsline{toc}{chapter}{Nomenclature}

\begin{tabular}{ll}
\textbf{Abbreviations} & \\
AEP & Annual Energy Production \\
AD & Automatic Differentiation \\
CMA-ES & Covariance Matrix Adaptation Evolution Strategy \\
DE & Differential Evolution \\
EA & Evolutionary Algorithm \\
\floris{} & FLOw Redirection and Induction in Steady-state \\
GA & Genetic Algorithm \\
\jax{} & Just Another XLA (Accelerated Linear Algebra) \\
NSGA & Non-dominated Sorting Genetic Algorithm \\
PSO & Particle Swarm Optimization \\
SLSQP & Sequential Least Squares Programming \\
\\
\textbf{Symbols} & \\
$\mathbf{x}$ & Turbine x-coordinates vector \\
$\mathbf{y}$ & Turbine y-coordinates vector \\
$P$ & Power output \\
$C_T$ & Thrust coefficient \\
$u$ & Wind velocity \\
$u_\infty$ & Free-stream wind velocity \\
$D$ & Rotor diameter \\
$\rho$ & Air density \\
$k$ & Wake expansion coefficient \\
$\delta$ & Velocity deficit \\
$f(\mathbf{x})$ & Objective function \\
$g(\mathbf{x})$ & Constraint function \\
$\nabla f$ & Gradient of objective function \\
\end{tabular}

% Main content
\pagenumbering{arabic}

% Chapters
\include{chapters/ch1_introduction}
\include{chapters/ch2_literature_review}
\include{chapters/ch3_theoretical_background}
\include{chapters/ch4_differentiable_floris}
\include{chapters/ch5_multi_framework}
\include{chapters/ch6_hybrid_optimization}
\include{chapters/ch7_implementation_results}
\include{chapters/ch8_discussion}
\include{chapters/ch9_conclusions}

% Bibliography
\bibliographystyle{IEEEtran}
\bibliography{references}

% Appendices
\begin{appendix}
\include{appendices/app_a_code_listings}
\include{appendices/app_b_additional_results}
\include{appendices/app_c_derivations}
\end{appendix}

\end{document}