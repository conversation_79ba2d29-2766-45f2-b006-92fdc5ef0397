\chapter{Mathematical Derivations}
\label{app:derivations}

This appendix provides detailed mathematical derivations for key results used in the thesis.

\section{Smooth Approximation Error Bounds}

\subsection{Sigmoid Approximation of Step Function}

The sigmoid function approximates a step function:
\begin{equation}
\sigma_k(x) = \frac{1}{1 + e^{-kx}}
\end{equation}

\textbf{Theorem:} The approximation error is bounded by:
\begin{equation}
|\text{step}(x) - \sigma_k(x)| \leq \frac{1}{2k|x|} \quad \text{for } |x| > \delta
\end{equation}

\textbf{Proof:}
For $x > 0$:
\begin{align}
\text{step}(x) - \sigma_k(x) &= 1 - \frac{1}{1 + e^{-kx}} \\
&= \frac{e^{-kx}}{1 + e^{-kx}} \\
&\leq e^{-kx}
\end{align}

Taking the Taylor expansion...

\section{Wake Model Gradient Derivation}

\subsection{Jensen Model Gradient}

The Jensen wake model velocity deficit:
\begin{equation}
\delta = \frac{1 - \sqrt{1 - C_T}}{(1 + kx/r_0)^2}
\end{equation}

The gradient with respect to downstream position $x$:
\begin{align}
\frac{\partial \delta}{\partial x} &= \frac{\partial}{\partial x}\left[\frac{1 - \sqrt{1 - C_T}}{(1 + kx/r_0)^2}\right] \\
&= (1 - \sqrt{1 - C_T}) \cdot \frac{-2(1 + kx/r_0)^{-3} \cdot k/r_0}{1} \\
&= \frac{-2k(1 - \sqrt{1 - C_T})}{r_0(1 + kx/r_0)^3}
\end{align}

\section{Optimal Budget Allocation}

\subsection{Sequential Hybrid Optimization}

Given total budget $B$, find optimal allocation between evolutionary ($B_e$) and gradient ($B_g = B - B_e$) stages.

\textbf{Assumption:} Expected improvement follows:
\begin{align}
I_e(B_e) &= \alpha_e(1 - e^{-\beta_e B_e}) \quad \text{(evolutionary)} \\
I_g(B_g) &= \alpha_g(1 - e^{-\beta_g B_g}) \quad \text{(gradient)}
\end{align}

The total improvement:
\begin{equation}
I_{total}(B_e) = I_e(B_e) + I_g(B - B_e) \cdot (1 - I_e(B_e))
\end{equation}

Taking the derivative and setting to zero:
\begin{equation}
\frac{dI_{total}}{dB_e} = \frac{dI_e}{dB_e}(1 - I_g) - \frac{dI_g}{dB_e}(1 - I_e) = 0
\end{equation}

This yields the optimal allocation...

\section{Constraint Handling in Gradient Methods}

\subsection{Augmented Lagrangian Formulation}

For constraints $g_i(\mathbf{x}) \leq 0$, the augmented Lagrangian:
\begin{equation}
\mathcal{L}_A(\mathbf{x}, \boldsymbol{\lambda}, \mu) = f(\mathbf{x}) + \sum_i \psi(g_i(\mathbf{x}), \lambda_i, \mu)
\end{equation}

where:
\begin{equation}
\psi(g, \lambda, \mu) = 
\begin{cases}
\lambda g + \frac{\mu}{2}g^2 & \text{if } g \geq -\lambda/\mu \\
-\frac{\lambda^2}{2\mu} & \text{otherwise}
\end{cases}
\end{equation}

The gradient:
\begin{equation}
\nabla_x \mathcal{L}_A = \nabla f + \sum_i \psi'(g_i, \lambda_i, \mu) \nabla g_i
\end{equation}