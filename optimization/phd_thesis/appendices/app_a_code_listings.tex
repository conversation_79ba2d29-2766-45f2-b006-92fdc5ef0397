\chapter{Code Listings}
\label{app:code_listings}

This appendix contains complete code listings for the key components developed in this thesis.

\section{DifferentiableFLORIS Core Implementation}

\begin{lstlisting}[language=Python,caption={Complete DifferentiableFLORIS implementation},label={lst:differentiable_floris}]
#!/usr/bin/env python3
"""
DifferentiableFLORIS.py - JAX-based differentiable wind farm model
"""

import jax
import jax.numpy as jnp
from jax import grad, jit, vmap
import numpy as np

# Smooth approximation functions
@jit
def smooth_sigmoid(x, k=10.0):
    """Smooth sigmoid function for replacing sharp conditionals"""
    return 1.0 / (1.0 + jnp.exp(-k * x))

@jit
def smooth_power_curve(ws, turbine_params=None):
    """Smooth, differentiable power curve using sigmoid functions."""
    # Implementation details shown in Chapter 4
    pass

# Full implementation available at:
# https://github.com/username/DifferentiableFLORIS
\end{lstlisting}

\section{Multi-Framework Optimization Interface}

\begin{lstlisting}[language=Python,caption={Unified optimization interface},label={lst:multi_framework}]
class UnifiedOptimizationInterface:
    """
    Provides consistent interface for DEAP, PyMoo, and Nevergrad
    """
    def __init__(self, framework='nevergrad'):
        self.framework = framework
        self._setup_framework()
        
    def optimize(self, problem, algorithm, budget):
        """Run optimization with specified framework"""
        if self.framework == 'nevergrad':
            return self._optimize_nevergrad(problem, algorithm, budget)
        elif self.framework == 'pymoo':
            return self._optimize_pymoo(problem, algorithm, budget)
        elif self.framework == 'deap':
            return self._optimize_deap(problem, algorithm, budget)
\end{lstlisting}

\section{Hybrid Optimization Strategy}

\begin{lstlisting}[language=Python,caption={Sequential hybrid optimization},label={lst:hybrid_sequential}]
class SequentialHybridStrategy:
    """Sequential evolutionary then gradient strategy"""
    
    def optimize(self):
        # Stage 1: Evolutionary optimization
        evo_result = self.run_evolutionary_stage()
        
        # Stage 2: Gradient refinement
        grad_result = self.run_gradient_stage(evo_result['best_solution'])
        
        return self.combine_results(evo_result, grad_result)
\end{lstlisting}