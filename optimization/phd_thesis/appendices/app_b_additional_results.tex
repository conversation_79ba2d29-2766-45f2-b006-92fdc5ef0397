\chapter{Additional Results}
\label{app:additional_results}

This appendix presents additional experimental results and detailed performance metrics not included in the main text.

\section{Extended Algorithm Comparison}

Table \ref{tab:extended_comparison} shows the complete results for all tested algorithms across different wind farm sizes.

\begin{table}[h]
\centering
\caption{Extended algorithm comparison results}
\label{tab:extended_comparison}
\begin{tabular}{lcccccc}
\toprule
Algorithm & 10 Turb. & 25 Turb. & 50 Turb. & 100 Turb. & Avg. Time (s) & Success Rate \\
\midrule
\multicolumn{7}{l}{\textit{Pure Evolutionary}} \\
NSGA-II & 98.2\% & 97.5\% & 96.8\% & 95.2\% & 245.3 & 100\% \\
NSGA-III & 98.5\% & 97.8\% & 97.1\% & 95.9\% & 267.8 & 100\% \\
JADE & 99.1\% & 98.3\% & 97.4\% & 96.1\% & 198.4 & 100\% \\
CMA-ES & 98.8\% & 98.1\% & 96.9\% & 94.8\% & 312.6 & 95\% \\
PSO & 97.6\% & 96.9\% & 95.8\% & 93.4\% & 178.9 & 98\% \\
\midrule
\multicolumn{7}{l}{\textit{Gradient-Based}} \\
L-BFGS-B & 96.2\%* & 94.8\%* & 92.1\%* & 89.3\%* & 45.2 & 75\% \\
SLSQP & 95.8\%* & 94.3\%* & 91.7\%* & 88.9\%* & 52.7 & 72\% \\
\midrule
\multicolumn{7}{l}{\textit{Hybrid Methods}} \\
Sequential & 99.3\% & 98.7\% & 98.1\% & 97.2\% & 156.8 & 100\% \\
Interleaved & 99.1\% & 98.5\% & 97.9\% & 96.8\% & 189.4 & 100\% \\
Gradient-Informed & 99.4\% & 98.8\% & 98.2\% & 97.5\% & 201.3 & 100\% \\
\bottomrule
\end{tabular}
\end{table}

*Results depend heavily on initial solution quality

\section{Constraint Satisfaction Analysis}

Figure \ref{fig:constraint_violations_extended} shows the distribution of constraint violations across different problem sizes.

\begin{figure}[h]
    \centering
    % Placeholder for actual figure
    \framebox[0.8\textwidth][c]{\parbox{0.75\textwidth}{\centering
        \vspace{3cm}
        Distribution of Constraint Violations\\
        \small (Actual figure to be generated)
        \vspace{3cm}
    }}
    \caption{Distribution of spacing constraint violations for different farm sizes and algorithms}
    \label{fig:constraint_violations_extended}
\end{figure}

\section{Computational Scaling Analysis}

The computational requirements scale differently for each approach:

\begin{itemize}
    \item \textbf{Evolutionary}: $O(p \cdot g \cdot n^2)$ where $p$ is population size, $g$ is generations
    \item \textbf{Gradient}: $O(i \cdot n^3)$ for quasi-Newton methods with $i$ iterations
    \item \textbf{Hybrid}: Combines both complexities with reduced constants
\end{itemize}

\section{Sensitivity Analysis}

Table \ref{tab:sensitivity_analysis} shows algorithm sensitivity to parameter variations.

\begin{table}[h]
\centering
\caption{Parameter sensitivity analysis}
\label{tab:sensitivity_analysis}
\begin{tabular}{lccc}
\toprule
Parameter & Variation & Impact on AEP & Impact on Time \\
\midrule
Population Size & ±50\% & ±1.2\% & ±45\% \\
Mutation Rate & ±50\% & ±2.1\% & ±5\% \\
Crossover Rate & ±50\% & ±0.8\% & ±3\% \\
Gradient Tolerance & ±1 order & ±0.3\% & ±25\% \\
Budget Allocation & 60/40 to 80/20 & ±1.5\% & ±15\% \\
\bottomrule
\end{tabular}
\end{table}